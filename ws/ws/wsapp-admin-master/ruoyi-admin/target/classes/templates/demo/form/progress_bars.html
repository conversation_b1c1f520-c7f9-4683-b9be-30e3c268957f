<!DOCTYPE html>
<html lang="zh">
<head>
	<th:block th:include="include :: header('进度条')" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>进度条 (Progress Bars)</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li><a href="#">选项1</a>
                                </li>
                                <li><a href="#">选项2</a>
                                </li>
                            </ul>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- style: progress-bar-success/progress-bar-info/progress-bar-warning/progress-bar-danger -->
                        <h5>基本</h5>
                        <div class="progress">
                            <div style="width: 35%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="35" role="progressbar" class="progress-bar progress-bar-success">
                                <span class="sr-only">35% Complete (success)</span>
                            </div>
                        </div>

                        <div class="progress progress-bar-default">
                            <div style="width: 43%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="43" role="progressbar" class="progress-bar">
                                <span class="sr-only">43% Complete (success)</span>
                            </div>
                        </div>

                        <h5>条纹效果</h5>
                        <div class="progress progress-striped">
                            <div style="width: 50%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="50" role="progressbar" class="progress-bar progress-bar-warning">
                                <span class="sr-only">50% Complete (success)</span>
                            </div>
                        </div>

                        <h5>动画效果</h5>
                        <div class="progress progress-striped active">
                            <div style="width: 75%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="75" role="progressbar" class="progress-bar progress-bar-danger">
                                <span class="sr-only">75% Complete (success)</span>
                            </div>
                        </div>

                        <h5>堆叠效果</h5>
                        <div class="progress progress-striped active">
                            <div style="width: 30%" class="progress-bar progress-bar-success">
                                <span class="sr-only">30% Complete (success)</span>
                            </div>
                            <div style="width: 20%" class="progress-bar progress-bar-warning">
                                <span class="sr-only">20% Complete (warning)</span>
                            </div>
                            <div style="width: 40%" class="progress-bar progress-bar-danger">
                                <span class="sr-only">40% Complete (danger)</span>
                            </div>
                        </div>
                        
                        <h5>带有提示标签的进度条</h5>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100" style="width: 95%;">
                                95%
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    $("[data-toggle='tooltip']").tooltip();
	    $("[data-toggle=popover]").popover();
    </script>
</body>
</html>
