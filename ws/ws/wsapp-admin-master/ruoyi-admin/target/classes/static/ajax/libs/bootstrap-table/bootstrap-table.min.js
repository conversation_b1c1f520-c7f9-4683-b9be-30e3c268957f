/**
 * <AUTHOR> wen <<EMAIL>>
 * version: 1.19.1
 * https://github.com/wenzhixin/bootstrap-table/
 */
function getRememberRowIds(t,e){return $.isArray(t)?props=$.map(t,function(t){return t[e]}):props=[t[e]],props}function addRememberRow(t,e){var i=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,n=getRememberRowIds(t,i);-1==$.inArray(e[i],n)&&(t[t.length]=e)}function removeRememberRow(t,e){var i=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,n=getRememberRowIds(t,i),o=$.inArray(e[i],n);-1!=o&&t.splice(o,1)}!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self,t.BootstrapTable=e(t.jQuery))}(this,function(t){function e(t){return t&&"object"==typeof t&&"default" in t?t:{"default":t}}function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}function o(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value" in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a(t,e,i){return e&&o(t.prototype,e),i&&o(t,i),t}function s(t,e){return c(t)||u(t,e)||d(t,e)||g()}function r(t){return l(t)||h(t)||d(t)||p()}function l(t){return Array.isArray(t)?f(t):void 0}function c(t){return Array.isArray(t)?t:void 0}function h(t){return"undefined"!=typeof Symbol&&Symbol.iterator in Object(t)?Array.from(t):void 0}function u(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var i=[],n=!0,o=!1,a=void 0;try{for(var s,r=t[Symbol.iterator]();!(n=(s=r.next()).done)&&(i.push(s.value),!e||i.length!==e);n=!0){}}catch(l){o=!0,a=l}finally{try{n||null==r["return"]||r["return"]()}finally{if(o){throw a}}}return i}}function d(t,e){if(t){if("string"==typeof t){return f(t,e)}var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++){n[i]=t[i]}return n}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){var i;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=d(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,r=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return s=t.done,t},e:function(t){r=!0,a=t},f:function(){try{s||null==i["return"]||i["return"]()}finally{if(r){throw a}}}}}function b(t,e){return e={exports:{}},t(e,e.exports),e.exports}function m(t,e){return RegExp(t,e)}var y=e(t),w="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},S=function(t){return t&&t.Math==Math&&t},x=S("object"==typeof globalThis&&globalThis)||S("object"==typeof window&&window)||S("object"==typeof self&&self)||S("object"==typeof w&&w)||function(){return this}()||Function("return this")(),k=function(t){try{return !!t()}catch(e){return !0}},O=!k(function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}),T={}.propertyIsEnumerable,C=Object.getOwnPropertyDescriptor,P=C&&!T.call({1:2},1),I=P?function(t){var e=C(this,t);return !!e&&e.enumerable}:T,A={f:I},$=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},R={}.toString,E=function(t){return R.call(t).slice(8,-1)},j="".split,_=k(function(){return !Object("z").propertyIsEnumerable(0)})?function(t){return"String"==E(t)?j.call(t,""):Object(t)}:Object,N=function(t){if(void 0==t){throw TypeError("Can't call method on "+t)}return t},F=function(t){return _(N(t))},D=function(t){return"object"==typeof t?null!==t:"function"==typeof t},V=function(t,e){if(!D(t)){return t}var i,n;if(e&&"function"==typeof(i=t.toString)&&!D(n=i.call(t))){return n}if("function"==typeof(i=t.valueOf)&&!D(n=i.call(t))){return n}if(!e&&"function"==typeof(i=t.toString)&&!D(n=i.call(t))){return n}throw TypeError("Can't convert object to primitive value")},B={}.hasOwnProperty,L=function(t,e){return B.call(t,e)},H=x.document,M=D(H)&&D(H.createElement),U=function(t){return M?H.createElement(t):{}},q=!O&&!k(function(){return 7!=Object.defineProperty(U("div"),"a",{get:function(){return 7}}).a}),z=Object.getOwnPropertyDescriptor,W=O?z:function(t,e){if(t=F(t),e=V(e,!0),q){try{return z(t,e)}catch(i){}}return L(t,e)?$(!A.f.call(t,e),t[e]):void 0},G={f:W},K=function(t){if(!D(t)){throw TypeError(t+" is not an object")}return t},Y=Object.defineProperty,X=O?Y:function(t,e,i){if(K(t),e=V(e,!0),K(i),q){try{return Y(t,e,i)}catch(n){}}if("get" in i||"set" in i){throw TypeError("Accessors not supported")}return"value" in i&&(t[e]=i.value),t},J={f:X},Q=O?function(t,e,i){return J.f(t,e,$(1,i))}:function(t,e,i){return t[e]=i,t},Z=function(t,e){try{Q(x,t,e)}catch(i){x[t]=e}return e},tt="__core-js_shared__",et=x[tt]||Z(tt,{}),it=et,nt=Function.toString;"function"!=typeof it.inspectSource&&(it.inspectSource=function(t){return nt.call(t)});var ot,at,st,rt=it.inspectSource,lt=x.WeakMap,ct="function"==typeof lt&&/native code/.test(rt(lt)),ht=b(function(t){(t.exports=function(t,e){return it[t]||(it[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.10.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})}),ut=0,dt=Math.random(),ft=function(t){return"Symbol("+((void 0===t?"":t)+"")+")_"+(++ut+dt).toString(36)},pt=ht("keys"),gt=function(t){return pt[t]||(pt[t]=ft(t))},vt={},bt=x.WeakMap,mt=function(t){return st(t)?at(t):ot(t,{})},yt=function(t){return function(e){var i;if(!D(e)||(i=at(e)).type!==t){throw TypeError("Incompatible receiver, "+t+" required")}return i}};if(ct){var wt=it.state||(it.state=new bt),St=wt.get,xt=wt.has,kt=wt.set;ot=function(t,e){return e.facade=t,kt.call(wt,t,e),e},at=function(t){return St.call(wt,t)||{}},st=function(t){return xt.call(wt,t)}}else{var Ot=gt("state");vt[Ot]=!0,ot=function(t,e){return e.facade=t,Q(t,Ot,e),e},at=function(t){return L(t,Ot)?t[Ot]:{}},st=function(t){return L(t,Ot)}}var Tt={set:ot,get:at,has:st,enforce:mt,getterFor:yt},Ct=b(function(t){var e=Tt.get,i=Tt.enforce,n=(String+"").split("String");(t.exports=function(t,e,o,a){var s,r=a?!!a.unsafe:!1,l=a?!!a.enumerable:!1,c=a?!!a.noTargetGet:!1;return"function"==typeof o&&("string"!=typeof e||L(o,"name")||Q(o,"name",e),s=i(o),s.source||(s.source=n.join("string"==typeof e?e:""))),t===x?void (l?t[e]=o:Z(e,o)):(r?!c&&t[e]&&(l=!0):delete t[e],void (l?t[e]=o:Q(t,e,o)))})(Function.prototype,"toString",function(){return"function"==typeof this&&e(this).source||rt(this)})}),Pt=x,It=function(t){return"function"==typeof t?t:void 0},At=function(t,e){return arguments.length<2?It(Pt[t])||It(x[t]):Pt[t]&&Pt[t][e]||x[t]&&x[t][e]},$t=Math.ceil,Rt=Math.floor,Et=function(t){return isNaN(t=+t)?0:(t>0?Rt:$t)(t)},jt=Math.min,_t=function(t){return t>0?jt(Et(t),9007199254740991):0},Nt=Math.max,Ft=Math.min,Dt=function(t,e){var i=Et(t);return 0>i?Nt(i+e,0):Ft(i,e)},Vt=function(t){return function(e,i,n){var o,a=F(e),s=_t(a.length),r=Dt(n,s);if(t&&i!=i){for(;s>r;){if(o=a[r++],o!=o){return !0}}}else{for(;s>r;r++){if((t||r in a)&&a[r]===i){return t||r||0}}}return !t&&-1}},Bt={includes:Vt(!0),indexOf:Vt(!1)},Lt=Bt.indexOf,Ht=function(t,e){var i,n=F(t),o=0,a=[];for(i in n){!L(vt,i)&&L(n,i)&&a.push(i)}for(;e.length>o;){L(n,i=e[o++])&&(~Lt(a,i)||a.push(i))}return a},Mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ut=Mt.concat("length","prototype"),qt=Object.getOwnPropertyNames||function(t){return Ht(t,Ut)},zt={f:qt},Wt=Object.getOwnPropertySymbols,Gt={f:Wt},Kt=At("Reflect","ownKeys")||function(t){var e=zt.f(K(t)),i=Gt.f;return i?e.concat(i(t)):e},Yt=function(t,e){for(var i=Kt(e),n=J.f,o=G.f,a=0;a<i.length;a++){var s=i[a];L(t,s)||n(t,s,o(e,s))}},Xt=/#|\.prototype\./,Jt=function(t,e){var i=Zt[Qt(t)];return i==ee?!0:i==te?!1:"function"==typeof e?k(e):!!e},Qt=Jt.normalize=function(t){return(t+"").replace(Xt,".").toLowerCase()},Zt=Jt.data={},te=Jt.NATIVE="N",ee=Jt.POLYFILL="P",ie=Jt,ne=G.f,oe=function(t,e){var i,n,o,a,s,r,l=t.target,c=t.global,h=t.stat;if(n=c?x:h?x[l]||Z(l,{}):(x[l]||{}).prototype){for(o in e){if(s=e[o],t.noTargetGet?(r=ne(n,o),a=r&&r.value):a=n[o],i=ie(c?o:l+(h?".":"#")+o,t.forced),!i&&void 0!==a){if(typeof s==typeof a){continue}Yt(s,a)}(t.sham||a&&a.sham)&&Q(s,"sham",!0),Ct(n,o,s,t)}}},ae="	\n\x0B\f\r                　\u2028\u2029\ufeff",se="["+ae+"]",re=RegExp("^"+se+se+"*"),le=RegExp(se+se+"*$"),ce=function(t){return function(e){var i=N(e)+"";return 1&t&&(i=i.replace(re,"")),2&t&&(i=i.replace(le,"")),i}},he={start:ce(1),end:ce(2),trim:ce(3)},ue="᠎",de=function(t){return k(function(){return !!ae[t]()||ue[t]()!=ue||ae[t].name!==t})},fe=he.trim;oe({target:"String",proto:!0,forced:de("trim")},{trim:function(){return fe(this)}});var pe=function(t,e){var i=[][t];return !!i&&k(function(){i.call(null,e||function(){throw 1},1)})},ge=[].join,ve=_!=Object,be=pe("join",",");oe({target:"Array",proto:!0,forced:ve||!be},{join:function(t){return ge.call(F(this),void 0===t?",":t)}});var me=function(){var t=K(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},ye=k(function(){var t=m("a","y");return t.lastIndex=2,null!=t.exec("abcd")}),we=k(function(){var t=m("^r","gy");return t.lastIndex=2,null!=t.exec("str")}),Se={UNSUPPORTED_Y:ye,BROKEN_CARET:we},xe=RegExp.prototype.exec,ke=ht("native-string-replace",String.prototype.replace),Oe=xe,Te=function(){var t=/a/,e=/b*/g;return xe.call(t,"a"),xe.call(e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),Ce=Se.UNSUPPORTED_Y||Se.BROKEN_CARET,Pe=void 0!==/()??/.exec("")[1],Ie=Te||Pe||Ce;Ie&&(Oe=function(t){var e,i,n,o,a=this,s=Ce&&a.sticky,r=me.call(a),l=a.source,c=0,h=t;return s&&(r=r.replace("y",""),-1===r.indexOf("g")&&(r+="g"),h=(t+"").slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==t[a.lastIndex-1])&&(l="(?: "+l+")",h=" "+h,c++),i=RegExp("^(?:"+l+")",r)),Pe&&(i=RegExp("^"+l+"$(?!\\s)",r)),Te&&(e=a.lastIndex),n=xe.call(s?i:a,h),s?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=a.lastIndex,a.lastIndex+=n[0].length):a.lastIndex=0:Te&&n&&(a.lastIndex=a.global?n.index+n[0].length:e),Pe&&n&&n.length>1&&ke.call(n[0],i,function(){for(o=1;o<arguments.length-2;o++){void 0===arguments[o]&&(n[o]=void 0)}}),n});var Ae=Oe;oe({target:"RegExp",proto:!0,forced:/./.exec!==Ae},{exec:Ae});var $e,Re,Ee="process"==E(x.process),je=At("navigator","userAgent")||"",_e=x.process,Ne=_e&&_e.versions,Fe=Ne&&Ne.v8;Fe?($e=Fe.split("."),Re=$e[0]+$e[1]):je&&($e=je.match(/Edge\/(\d+)/),(!$e||$e[1]>=74)&&($e=je.match(/Chrome\/(\d+)/),$e&&(Re=$e[1])));var De=Re&&+Re,Ve=!!Object.getOwnPropertySymbols&&!k(function(){return !Symbol.sham&&(Ee?38===De:De>37&&41>De)}),Be=Ve&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Le=ht("wks"),He=x.Symbol,Me=Be?He:He&&He.withoutSetter||ft,Ue=function(t){return(!L(Le,t)||!Ve&&"string"!=typeof Le[t])&&(Ve&&L(He,t)?Le[t]=He[t]:Le[t]=Me("Symbol."+t)),Le[t]},qe=Ue("species"),ze=!k(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}),We=function(){return"$0"==="a".replace(/./,"$0")}(),Ge=Ue("replace"),Ke=function(){return/./[Ge]?""===/./[Ge]("a","$0"):!1}(),Ye=!k(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]}),Xe=function(t,e,i,n){var o=Ue(t),a=!k(function(){var e={};return e[o]=function(){return 7},7!=""[t](e)}),s=a&&!k(function(){var e=!1,i=/a/;return"split"===t&&(i={},i.constructor={},i.constructor[qe]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e});if(!a||!s||"replace"===t&&(!ze||!We||Ke)||"split"===t&&!Ye){var r=/./[o],l=i(o,""[t],function(t,e,i,n,o){return e.exec===RegExp.prototype.exec?a&&!o?{done:!0,value:r.call(e,i,n)}:{done:!0,value:t.call(i,e,n)}:{done:!1}},{REPLACE_KEEPS_$0:We,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:Ke}),c=l[0],h=l[1];Ct(String.prototype,t,c),Ct(RegExp.prototype,o,2==e?function(t,e){return h.call(t,this,e)}:function(t){return h.call(t,this)})}n&&Q(RegExp.prototype[o],"sham",!0)},Je=Ue("match"),Qe=function(t){var e;return D(t)&&(void 0!==(e=t[Je])?!!e:"RegExp"==E(t))},Ze=function(t){if("function"!=typeof t){throw TypeError(t+" is not a function")}return t},ti=Ue("species"),ei=function(t,e){var i,n=K(t).constructor;return void 0===n||void 0==(i=K(n)[ti])?e:Ze(i)},ii=function(t){return function(e,i){var n,o,a=N(e)+"",s=Et(i),r=a.length;return 0>s||s>=r?t?"":void 0:(n=a.charCodeAt(s),55296>n||n>56319||s+1===r||(o=a.charCodeAt(s+1))<56320||o>57343?t?a.charAt(s):n:t?a.slice(s,s+2):(n-55296<<10)+(o-56320)+65536)}},ni={codeAt:ii(!1),charAt:ii(!0)},oi=ni.charAt,ai=function(t,e,i){return e+(i?oi(t,e).length:1)},si=function(t,e){var i=t.exec;if("function"==typeof i){var n=i.call(t,e);if("object"!=typeof n){throw TypeError("RegExp exec method returned something other than an Object or null")}return n}if("RegExp"!==E(t)){throw TypeError("RegExp#exec called on incompatible receiver")}return Ae.call(t,e)},ri=Se.UNSUPPORTED_Y,li=[].push,ci=Math.min,hi=4294967295;Xe("split",2,function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=N(this)+"",o=void 0===i?hi:i>>>0;if(0===o){return[]}if(void 0===t){return[n]}if(!Qe(t)){return e.call(n,t,o)}for(var a,s,r,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=RegExp(t.source,c+"g");(a=Ae.call(u,n))&&(s=u.lastIndex,!(s>h&&(l.push(n.slice(h,a.index)),a.length>1&&a.index<n.length&&li.apply(l,a.slice(1)),r=a[0].length,h=s,l.length>=o)));){u.lastIndex===a.index&&u.lastIndex++}return h===n.length?(r||!u.test(""))&&l.push(""):l.push(n.slice(h)),l.length>o?l.slice(0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,i){var o=N(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,o,i):n.call(o+"",e,i)},function(t,o){var a=i(n,t,this,o,n!==e);if(a.done){return a.value}var s=K(t),r=this+"",l=ei(s,RegExp),c=s.unicode,h=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(ri?"g":"y"),u=new l(ri?"^(?:"+s.source+")":s,h),d=void 0===o?hi:o>>>0;if(0===d){return[]}if(0===r.length){return null===si(u,r)?[r]:[]}for(var f=0,p=0,g=[];p<r.length;){u.lastIndex=ri?0:p;var v,b=si(u,ri?r.slice(p):r);if(null===b||(v=ci(_t(u.lastIndex+(ri?p:0)),r.length))===f){p=ai(r,p,c)}else{if(g.push(r.slice(f,p)),g.length===d){return g}for(var m=1;m<=b.length-1;m++){if(g.push(b[m]),g.length===d){return g}}p=f=v}}return g.push(r.slice(f)),g}]},ri);var ui=Object.keys||function(t){return Ht(t,Mt)},di=A.f,fi=function(t){return function(e){for(var i,n=F(e),o=ui(n),a=o.length,s=0,r=[];a>s;){i=o[s++],(!O||di.call(n,i))&&r.push(t?[i,n[i]]:n[i])}return r}},pi={entries:fi(!0),values:fi(!1)},gi=pi.entries;oe({target:"Object",stat:!0},{entries:function(t){return gi(t)}});var vi,bi=O?Object.defineProperties:function(t,e){K(t);for(var i,n=ui(e),o=n.length,a=0;o>a;){J.f(t,i=n[a++],e[i])}return t},mi=At("document","documentElement"),yi=">",wi="<",Si="prototype",xi="script",ki=gt("IE_PROTO"),Oi=function(){},Ti=function(t){return wi+xi+yi+t+wi+"/"+xi+yi},Ci=function(t){t.write(Ti("")),t.close();var e=t.parentWindow.Object;return t=null,e},Pi=function(){var t,e=U("iframe"),i="java"+xi+":";return e.style.display="none",mi.appendChild(e),e.src=i+"",t=e.contentWindow.document,t.open(),t.write(Ti("document.F=Object")),t.close(),t.F},Ii=function(){try{vi=document.domain&&new ActiveXObject("htmlfile")}catch(t){}Ii=vi?Ci(vi):Pi();for(var e=Mt.length;e--;){delete Ii[Si][Mt[e]]}return Ii()};vt[ki]=!0;var Ai=Object.create||function(t,e){var i;return null!==t?(Oi[Si]=K(t),i=new Oi,Oi[Si]=null,i[ki]=t):i=Ii(),void 0===e?i:bi(i,e)},$i=Ue("unscopables"),Ri=Array.prototype;void 0==Ri[$i]&&J.f(Ri,$i,{configurable:!0,value:Ai(null)});var Ei=function(t){Ri[$i][t]=!0},ji=Bt.includes;oe({target:"Array",proto:!0},{includes:function(t){return ji(this,t,arguments.length>1?arguments[1]:void 0)}}),Ei("includes");var _i=Array.isArray||function(t){return"Array"==E(t)},Ni=function(t){return Object(N(t))},Fi=function(t,e,i){var n=V(e);n in t?J.f(t,n,$(0,i)):t[n]=i},Di=Ue("species"),Vi=function(t,e){var i;return _i(t)&&(i=t.constructor,"function"!=typeof i||i!==Array&&!_i(i.prototype)?D(i)&&(i=i[Di],null===i&&(i=void 0)):i=void 0),new (void 0===i?Array:i)(0===e?0:e)},Bi=Ue("species"),Li=function(t){return De>=51||!k(function(){var e=[],i=e.constructor={};return i[Bi]=function(){return{foo:1}},1!==e[t](Boolean).foo})},Hi=Ue("isConcatSpreadable"),Mi=9007199254740991,Ui="Maximum allowed index exceeded",qi=De>=51||!k(function(){var t=[];return t[Hi]=!1,t.concat()[0]!==t}),zi=Li("concat"),Wi=function(t){if(!D(t)){return !1}var e=t[Hi];return void 0!==e?!!e:_i(t)},Gi=!qi||!zi;oe({target:"Array",proto:!0,forced:Gi},{concat:function(t){var e,i,n,o,a,s=Ni(this),r=Vi(s,0),l=0;for(e=-1,n=arguments.length;n>e;e++){if(a=-1===e?s:arguments[e],Wi(a)){if(o=_t(a.length),l+o>Mi){throw TypeError(Ui)}for(i=0;o>i;i++,l++){i in a&&Fi(r,l,a[i])}}else{if(l>=Mi){throw TypeError(Ui)}Fi(r,l++,a)}}return r.length=l,r}});var Ki=function(t,e,i){if(Ze(t),void 0===e){return t}switch(i){case 0:return function(){return t.call(e)};case 1:return function(i){return t.call(e,i)};case 2:return function(i,n){return t.call(e,i,n)};case 3:return function(i,n,o){return t.call(e,i,n,o)}}return function(){return t.apply(e,arguments)}},Yi=[].push,Xi=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,a=6==t,s=7==t,r=5==t||a;return function(l,c,h,u){for(var d,f,p=Ni(l),g=_(p),v=Ki(c,h,3),b=_t(g.length),m=0,y=u||Vi,w=e?y(l,b):i||s?y(l,0):void 0;b>m;m++){if((r||m in g)&&(d=g[m],f=v(d,m,p),t)){if(e){w[m]=f}else{if(f){switch(t){case 3:return !0;case 5:return d;case 6:return m;case 2:Yi.call(w,d)}}else{switch(t){case 4:return !1;case 7:Yi.call(w,d)}}}}}return a?-1:n||o?o:w}},Ji={forEach:Xi(0),map:Xi(1),filter:Xi(2),some:Xi(3),every:Xi(4),find:Xi(5),findIndex:Xi(6),filterOut:Xi(7)},Qi=Ji.find,Zi="find",tn=!0;Zi in []&&Array(1)[Zi](function(){tn=!1}),oe({target:"Array",proto:!0,forced:tn},{find:function(t){return Qi(this,t,arguments.length>1?arguments[1]:void 0)}}),Ei(Zi);var en=function(t){if(Qe(t)){throw TypeError("The method doesn't accept regular expressions")}return t},nn=Ue("match"),on=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[nn]=!1,"/./"[t](e)}catch(n){}}return !1};oe({target:"String",proto:!0,forced:!on("includes")},{includes:function(t){return !!~(N(this)+"").indexOf(en(t),arguments.length>1?arguments[1]:void 0)}});var an={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},sn=Ji.forEach,rn=pe("forEach"),ln=rn?[].forEach:function(t){return sn(this,t,arguments.length>1?arguments[1]:void 0)};for(var cn in an){var hn=x[cn],un=hn&&hn.prototype;if(un&&un.forEach!==ln){try{Q(un,"forEach",ln)}catch(dn){un.forEach=ln}}}var fn=he.trim,pn=x.parseFloat,gn=1/pn(ae+"-0")!==-(1/0),vn=gn?function(t){var e=fn(t+""),i=pn(e);return 0===i&&"-"==e.charAt(0)?-0:i}:pn;oe({global:!0,forced:parseFloat!=vn},{parseFloat:vn});var bn=Bt.indexOf,mn=[].indexOf,yn=!!mn&&1/[1].indexOf(1,-0)<0,wn=pe("indexOf");oe({target:"Array",proto:!0,forced:yn||!wn},{indexOf:function(t){return yn?mn.apply(this,arguments)||0:bn(this,t,arguments.length>1?arguments[1]:void 0)}});var Sn=[],xn=Sn.sort,kn=k(function(){Sn.sort(void 0)}),On=k(function(){Sn.sort(null)}),Tn=pe("sort"),Cn=kn||!On||!Tn;oe({target:"Array",proto:!0,forced:Cn},{sort:function(t){return void 0===t?xn.call(Ni(this)):xn.call(Ni(this),Ze(t))}});var Pn=Math.floor,In="".replace,An=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,$n=/\$([$&'`]|\d{1,2})/g,Rn=function(t,e,i,n,o,a){var s=i+t.length,r=n.length,l=$n;return void 0!==o&&(o=Ni(o),l=An),In.call(a,l,function(a,l){var c;switch(l.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,i);case"'":return e.slice(s);case"<":c=o[l.slice(1,-1)];break;default:var h=+l;if(0===h){return a}if(h>r){var u=Pn(h/10);return 0===u?a:r>=u?void 0===n[u-1]?l.charAt(1):n[u-1]+l.charAt(1):a}c=n[h-1]}return void 0===c?"":c})},En=Math.max,jn=Math.min,_n=function(t){return void 0===t?t:t+""};Xe("replace",2,function(t,e,i,n){var o=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=n.REPLACE_KEEPS_$0,s=o?"$":"$0";return[function(i,n){var o=N(this),a=void 0==i?void 0:i[t];return void 0!==a?a.call(i,o,n):e.call(o+"",i,n)},function(t,n){if(!o&&a||"string"==typeof n&&-1===n.indexOf(s)){var r=i(e,t,this,n);if(r.done){return r.value}}var l=K(t),c=this+"",h="function"==typeof n;h||(n+="");var u=l.global;if(u){var d=l.unicode;l.lastIndex=0}for(var f=[];;){var p=si(l,c);if(null===p){break}if(f.push(p),!u){break}var g=p[0]+"";""===g&&(l.lastIndex=ai(c,_t(l.lastIndex),d))}for(var v="",b=0,m=0;m<f.length;m++){p=f[m];for(var y=p[0]+"",w=En(jn(Et(p.index),c.length),0),S=[],x=1;x<p.length;x++){S.push(_n(p[x]))}var k=p.groups;if(h){var O=[y].concat(S,w,c);void 0!==k&&O.push(k);var T=n.apply(void 0,O)+""}else{T=Rn(y,c,w,S,k,n)}w>=b&&(v+=c.slice(b,w)+T,b=w+y.length)}return v+c.slice(b)}]});var Nn=Object.assign,Fn=Object.defineProperty,Dn=!Nn||k(function(){if(O&&1!==Nn({b:1},Nn(Fn({},"a",{enumerable:!0,get:function(){Fn(this,"b",{value:3,enumerable:!1})}}),{b:2})).b){return !0}var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach(function(t){e[t]=t}),7!=Nn({},t)[i]||ui(Nn({},e)).join("")!=n})?function(t,e){for(var i=Ni(t),n=arguments.length,o=1,a=Gt.f,s=A.f;n>o;){for(var r,l=_(arguments[o++]),c=a?ui(l).concat(a(l)):ui(l),h=c.length,u=0;h>u;){r=c[u++],(!O||s.call(l,r))&&(i[r]=l[r])}}return i}:Nn;oe({target:"Object",stat:!0,forced:Object.assign!==Dn},{assign:Dn});var Vn=Ji.filter,Bn=Li("filter");oe({target:"Array",proto:!0,forced:!Bn},{filter:function(t){return Vn(this,t,arguments.length>1?arguments[1]:void 0)}});var Ln=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e};Xe("search",1,function(t,e,i){return[function(e){var i=N(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,i):RegExp(e)[t](i+"")},function(t){var n=i(e,t,this);if(n.done){return n.value}var o=K(t),a=this+"",s=o.lastIndex;Ln(s,0)||(o.lastIndex=0);var r=si(o,a);return Ln(o.lastIndex,s)||(o.lastIndex=s),null===r?-1:r.index}]});var Hn=he.trim,Mn=x.parseInt,Un=/^[+-]?0[Xx]/,qn=8!==Mn(ae+"08")||22!==Mn(ae+"0x16"),zn=qn?function(t,e){var i=Hn(t+"");return Mn(i,e>>>0||(Un.test(i)?16:10))}:Mn;oe({global:!0,forced:parseInt!=zn},{parseInt:zn});var Wn=Ji.map,Gn=Li("map");oe({target:"Array",proto:!0,forced:!Gn},{map:function(t){return Wn(this,t,arguments.length>1?arguments[1]:void 0)}});var Kn=Ji.findIndex,Yn="findIndex",Xn=!0;Yn in []&&Array(1)[Yn](function(){Xn=!1}),oe({target:"Array",proto:!0,forced:Xn},{findIndex:function(t){return Kn(this,t,arguments.length>1?arguments[1]:void 0)}}),Ei(Yn);var Jn=function(t){if(!D(t)&&null!==t){throw TypeError("Can't set "+(t+"")+" as a prototype")}return t},Qn=Object.setPrototypeOf||("__proto__" in {}?function(){var t,e=!1,i={};try{t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,t.call(i,[]),e=i instanceof Array}catch(n){}return function(i,n){return K(i),Jn(n),e?t.call(i,n):i.__proto__=n,i}}():void 0),Zn=function(t,e,i){var n,o;return Qn&&"function"==typeof(n=e.constructor)&&n!==i&&D(o=n.prototype)&&o!==i.prototype&&Qn(t,o),t},to=Ue("species"),eo=function(t){var e=At(t),i=J.f;O&&e&&!e[to]&&i(e,to,{configurable:!0,get:function(){return this}})},io=J.f,no=zt.f,oo=Tt.set,ao=Ue("match"),so=x.RegExp,ro=so.prototype,lo=/a/g,co=/a/g,ho=new so(lo)!==lo,uo=Se.UNSUPPORTED_Y,fo=O&&ie("RegExp",!ho||uo||k(function(){return co[ao]=!1,so(lo)!=lo||so(co)==co||"/a/i"!=so(lo,"i")}));if(fo){for(var po=function(t,e){var i,n=this instanceof po,o=Qe(t),a=void 0===e;if(!n&&o&&t.constructor===po&&a){return t}ho?o&&!a&&(t=t.source):t instanceof po&&(a&&(e=me.call(t)),t=t.source),uo&&(i=!!e&&e.indexOf("y")>-1,i&&(e=e.replace(/y/g,"")));var s=Zn(ho?new so(t,e):so(t,e),n?this:ro,po);return uo&&i&&oo(s,{sticky:i}),s},go=(function(t){t in po||io(po,t,{configurable:!0,get:function(){return so[t]},set:function(e){so[t]=e}})}),vo=no(so),bo=0;vo.length>bo;){go(vo[bo++])}ro.constructor=po,po.prototype=ro,Ct(x,"RegExp",po)}eo("RegExp");var mo="toString",yo=RegExp.prototype,wo=yo[mo],So=k(function(){return"/a/b"!=wo.call({source:"a",flags:"b"})}),xo=wo.name!=mo;(So||xo)&&Ct(RegExp.prototype,mo,function(){var t=K(this),e=t.source+"",i=t.flags,n=(void 0===i&&t instanceof RegExp&&!("flags" in yo)?me.call(t):i)+"";return"/"+e+"/"+n},{unsafe:!0});var ko=Ue("toStringTag"),Oo={};Oo[ko]="z";var To=Oo+""=="[object z]",Co=Ue("toStringTag"),Po="Arguments"==E(function(){return arguments}()),Io=function(t,e){try{return t[e]}catch(i){}},Ao=To?E:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=Io(e=Object(t),Co))?i:Po?E(e):"Object"==(n=E(e))&&"function"==typeof e.callee?"Arguments":n},$o=To?{}.toString:function(){return"[object "+Ao(this)+"]"};To||Ct(Object.prototype,"toString",$o,{unsafe:!0});var Ro=Li("slice"),Eo=Ue("species"),jo=[].slice,_o=Math.max;oe({target:"Array",proto:!0,forced:!Ro},{slice:function(t,e){var i,n,o,a=F(this),s=_t(a.length),r=Dt(t,s),l=Dt(void 0===e?s:e,s);if(_i(a)&&(i=a.constructor,"function"!=typeof i||i!==Array&&!_i(i.prototype)?D(i)&&(i=i[Eo],null===i&&(i=void 0)):i=void 0,i===Array||void 0===i)){return jo.call(a,r,l)}for(n=new (void 0===i?Array:i)(_o(l-r,0)),o=0;l>r;r++,o++){r in a&&Fi(n,o,a[r])}return n.length=o,n}});var No,Fo,Do,Vo=!k(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}),Bo=gt("IE_PROTO"),Lo=Object.prototype,Ho=Vo?Object.getPrototypeOf:function(t){return t=Ni(t),L(t,Bo)?t[Bo]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?Lo:null},Mo=Ue("iterator"),Uo=!1,qo=function(){return this};[].keys&&(Do=[].keys(),"next" in Do?(Fo=Ho(Ho(Do)),Fo!==Object.prototype&&(No=Fo)):Uo=!0);var zo=void 0==No||k(function(){var t={};return No[Mo].call(t)!==t});zo&&(No={}),L(No,Mo)||Q(No,Mo,qo);var Wo={IteratorPrototype:No,BUGGY_SAFARI_ITERATORS:Uo},Go=J.f,Ko=Ue("toStringTag"),Yo=function(t,e,i){t&&!L(t=i?t:t.prototype,Ko)&&Go(t,Ko,{configurable:!0,value:e})},Xo=Wo.IteratorPrototype,Jo=function(t,e,i){var n=e+" Iterator";return t.prototype=Ai(Xo,{next:$(1,i)}),Yo(t,n,!1),t},Qo=Wo.IteratorPrototype,Zo=Wo.BUGGY_SAFARI_ITERATORS,ta=Ue("iterator"),ea="keys",ia="values",na="entries",oa=function(){return this},aa=function(t,e,i,n,o,a,s){Jo(i,e,n);var r,l,c,h=function(t){if(t===o&&g){return g}if(!Zo&&t in f){return f[t]}switch(t){case ea:return function(){return new i(this,t)};case ia:return function(){return new i(this,t)};case na:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,f=t.prototype,p=f[ta]||f["@@iterator"]||o&&f[o],g=!Zo&&p||h(o),v="Array"==e?f.entries||p:p;if(v&&(r=Ho(v.call(new t)),Qo!==Object.prototype&&r.next&&(Ho(r)!==Qo&&(Qn?Qn(r,Qo):"function"!=typeof r[ta]&&Q(r,ta,oa)),Yo(r,u,!0))),o==ia&&p&&p.name!==ia&&(d=!0,g=function(){return p.call(this)}),f[ta]!==g&&Q(f,ta,g),o){if(l={values:h(ia),keys:a?g:h(ea),entries:h(na)},s){for(c in l){!Zo&&!d&&c in f||Ct(f,c,l[c])}}else{oe({target:e,proto:!0,forced:Zo||d},l)}}return l},sa="Array Iterator",ra=Tt.set,la=Tt.getterFor(sa),ca=aa(Array,"Array",function(t,e){ra(this,{type:sa,target:F(t),index:0,kind:e})},function(){var t=la(this),e=t.target,i=t.kind,n=t.index++;return !e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:n,done:!1}:"values"==i?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}},"values");Ei("keys"),Ei("values"),Ei("entries");var ha=Ue("iterator"),ua=Ue("toStringTag"),da=ca.values;for(var fa in an){var pa=x[fa],ga=pa&&pa.prototype;if(ga){if(ga[ha]!==da){try{Q(ga,ha,da)}catch(dn){ga[ha]=da}}if(ga[ua]||Q(ga,ua,fa),an[fa]){for(var va in ca){if(ga[va]!==ca[va]){try{Q(ga,va,ca[va])}catch(dn){ga[va]=ca[va]}}}}}}var ba=Li("splice"),ma=Math.max,ya=Math.min,wa=9007199254740991,Sa="Maximum allowed length exceeded";oe({target:"Array",proto:!0,forced:!ba},{splice:function(t,e){var i,n,o,a,s,r,l=Ni(this),c=_t(l.length),h=Dt(t,c),u=arguments.length;if(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=ya(ma(Et(e),0),c-h)),c+i-n>wa){throw TypeError(Sa)}for(o=Vi(l,n),a=0;n>a;a++){s=h+a,s in l&&Fi(o,a,l[s])}if(o.length=n,n>i){for(a=h;c-n>a;a++){s=a+n,r=a+i,s in l?l[r]=l[s]:delete l[r]}for(a=c;a>c-n+i;a--){delete l[a-1]}}else{if(i>n){for(a=c-n;a>h;a--){s=a+n-1,r=a+i-1,s in l?l[r]=l[s]:delete l[r]}}}for(a=0;i>a;a++){l[a+h]=arguments[a+2]}return l.length=c-n+i,o}});var xa=zt.f,ka=G.f,Oa=J.f,Ta=he.trim,Ca="Number",Pa=x[Ca],Ia=Pa.prototype,Aa=E(Ai(Ia))==Ca,$a=function(t){var e,i,n,o,a,s,r,l,c=V(t,!1);if("string"==typeof c&&c.length>2){if(c=Ta(c),e=c.charCodeAt(0),43===e||45===e){if(i=c.charCodeAt(2),88===i||120===i){return NaN}}else{if(48===e){switch(c.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return +c}for(a=c.slice(2),s=a.length,r=0;s>r;r++){if(l=a.charCodeAt(r),48>l||l>o){return NaN}}return parseInt(a,n)}}}return +c};if(ie(Ca,!Pa(" 0o1")||!Pa("0b1")||Pa("+0x1"))){for(var Ra,Ea=function(t){var e=arguments.length<1?0:t,i=this;return i instanceof Ea&&(Aa?k(function(){Ia.valueOf.call(i)}):E(i)!=Ca)?Zn(new Pa($a(e)),i,Ea):$a(e)},ja=O?xa(Pa):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),_a=0;ja.length>_a;_a++){L(Pa,Ra=ja[_a])&&!L(Ea,Ra)&&Oa(Ea,Ra,ka(Pa,Ra))}Ea.prototype=Ia,Ia.constructor=Ea,Ct(x,Ca,Ea)}var Na=[].reverse,Fa=[1,2];oe({target:"Array",proto:!0,forced:Fa+""==Fa.reverse()+""},{reverse:function(){return _i(this)&&(this.length=this.length),Na.call(this)}});var Da="1.19.1",Va=4;try{var Ba=y["default"].fn.dropdown.Constructor.VERSION;void 0!==Ba&&(Va=parseInt(Ba,10))}catch(La){}try{var Ha=bootstrap.Tooltip.VERSION;void 0!==Ha&&(Va=parseInt(Ha,10))}catch(La){}var Ma={3:{iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{iconsPrefix:"bi",icons:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[Va],Ua={id:void 0,firstLoad:!0,height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",striped:!1,headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showSearch:!1,showPageGo:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return !0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["search","paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:Ma.classes.buttonsPrefix,buttonsClass:Ma.classes.buttons,icons:Ma.icons,iconSize:void 0,iconsPrefix:Ma.iconsPrefix,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return !1},onClickCell:function(t,e,i,n){return !1},onDblClickCell:function(t,e,i,n){return !1},onClickRow:function(t,e){return !1},onDblClickRow:function(t,e){return !1},onSort:function(t,e){return !1},onCheck:function(t){return !1},onUncheck:function(t){return !1},onCheckAll:function(t){return !1},onUncheckAll:function(t){return !1},onCheckSome:function(t){return !1},onUncheckSome:function(t){return !1},onLoadSuccess:function(t){return !1},onLoadError:function(t){return !1},onColumnSwitch:function(t,e){return !1},onPageChange:function(t,e){return !1},onSearch:function(t){return !1},onShowSearch:function(){return !1},onToggle:function(t){return !1},onPreBody:function(t){return !1},onPostBody:function(){return !1},onPostHeader:function(){return !1},onPostFooter:function(){return !1},onExpandRow:function(t,e,i){return !1},onCollapseRow:function(t,e){return !1},onRefreshOptions:function(t){return !1},onRefresh:function(t){return !1},onResetView:function(){return !1},onScrollBody:function(){return !1},onTogglePagination:function(t){return !1}},qa={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatShowSearch:function(){return"Show Search"},formatPageGo:function(){return"Go"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},za={field:void 0,title:void 0,titleTooltip:void 0,"class":void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,ignore:!1,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:!1,events:void 0},Wa=["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],Ga={"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"};Object.assign(Ua,qa);var Ka={VERSION:Da,THEME:"bootstrap".concat(Va),CONSTANTS:Ma,DEFAULTS:Ua,COLUMN_DEFAULTS:za,METHODS:Wa,EVENTS:Ga,LOCALES:{en:qa,"en-US":qa}},Ya=k(function(){ui(1)});oe({target:"Object",stat:!0,forced:Ya},{keys:function(t){return ui(Ni(t))}}),Xe("match",1,function(t,e,i){return[function(e){var i=N(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,i):RegExp(e)[t](i+"")},function(t){var n=i(e,t,this);if(n.done){return n.value}var o=K(t),a=this+"";if(!o.global){return si(o,a)}var s=o.unicode;o.lastIndex=0;for(var r,l=[],c=0;null!==(r=si(o,a));){var h=r[0]+"";l[c]=h,""===h&&(o.lastIndex=ai(a,_t(o.lastIndex),s)),c++}return 0===c?null:l}]});var Xa=G.f,Ja="".startsWith,Qa=Math.min,Za=on("startsWith"),ts=!Za&&!!function(){var t=Xa(String.prototype,"startsWith");return t&&!t.writable}();oe({target:"String",proto:!0,forced:!ts&&!Za},{startsWith:function(t){var e=N(this)+"";en(t);var i=_t(Qa(arguments.length>1?arguments[1]:void 0,e.length)),n=t+"";return Ja?Ja.call(e,n,i):e.slice(i,i+n.length)===n}});var es=G.f,is="".endsWith,ns=Math.min,os=on("endsWith"),as=!os&&!!function(){var t=es(String.prototype,"endsWith");return t&&!t.writable}();oe({target:"String",proto:!0,forced:!as&&!os},{endsWith:function(t){var e=N(this)+"";en(t);var i=arguments.length>1?arguments[1]:void 0,n=_t(e.length),o=void 0===i?n:ns(_t(i),n),a=t+"";return is?is.call(e,a,o):e.slice(o-a.length,o)===a}});var ss={getSearchInput:function(t){return"string"==typeof t.options.searchSelector?y["default"](t.options.searchSelector):t.$toolbar.find(".search input")},sprintf:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;e>n;n++){i[n-1]=arguments[n]}var o=!0,a=0,s=t.replace(/%s/g,function(){var t=i[a++];return void 0===t?(o=!1,""):t});return o?s:""},isObject:function(t){return t instanceof Object&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return !isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=v(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e){return o.title}}}catch(a){n.e(a)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=v(t[0]);try{for(o.s();!(e=o.n()).done;){var a=e.value;i+=a.colspan||1}}catch(s){o.e(s)}finally{o.f()}for(var r=0;r<t.length;r++){n[r]=[];for(var l=0;i>l;l++){n[r][l]=!1}}for(var c=0;c<t.length;c++){var h,u=v(t[c]);try{for(u.s();!(h=u.n()).done;){var d=h.value,f=d.rowspan||1,p=d.colspan||1,g=n[c].indexOf(!1);d.colspanIndex=g,1===p?(d.fieldIndex=g,void 0===d.field&&(d.field=g)):d.colspanGroup=d.colspan;for(var b=0;f>b;b++){for(var m=0;p>m;m++){n[c+b][g+m]=!0}}}}catch(s){u.e(s)}finally{u.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t){var e,i,n=(e=[]).concat.apply(e,r(t)),o=v(t);try{for(o.s();!(i=o.n()).done;){var a,s=i.value,l=v(s);try{for(l.s();!(a=l.n()).done;){var c=a.value;if(c.colspanGroup>1){for(var h=0,u=function(t){var e=n.find(function(e){return e.fieldIndex===t});e.visible&&h++},d=c.colspanIndex;d<c.colspanIndex+c.colspanGroup;d++){u(d)}c.colspan=h,c.visible=h>0}}}catch(f){l.e(f)}finally{l.f()}}}catch(f){o.e(f)}finally{o.f()}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var t=y["default"]("<div/>").addClass("fixed-table-scroll-inner"),e=y["default"]("<div/>").addClass("fixed-table-scroll-outer");e.append(t),y["default"]("body").append(e);var i=t[0].offsetWidth;e.css("overflow","scroll");var n=t[0].offsetWidth;i===n&&(n=e[0].clientWidth),e.remove(),this.cachedWidth=i-n}return this.cachedWidth},calculateObjectValue:function(t,e,n,o){var a=e;if("string"==typeof e){var s=e.split(".");if(s.length>1){a=window;var l,c=v(s);try{for(c.s();!(l=c.n()).done;){var h=l.value;a=a[h]}}catch(u){c.e(u)}finally{c.f()}}else{a=window[e]}}return null!==a&&"object"===i(a)?a:"function"==typeof a?a.apply(t,n||[]):!a&&"string"==typeof e&&this.sprintf.apply(this,[e].concat(r(n)))?this.sprintf.apply(this,[e].concat(r(n))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length){return !1}for(var a=0,s=n;a<s.length;a++){var r=s[a];if(o.includes(r)&&t[r]!==e[r]){return !1}}return !0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==(""+t).search(i?RegExp(i[1],i[2]):RegExp(e,"gim"))){return !0}}catch(n){return !1}},escapeHTML:function(t){return t?(""+t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return t?(""+t).replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=s(i[e],2),o=n[0],a=n[1],r=o.split(/(?=[A-Z])/).join("-").toLowerCase();r!==o&&(t[r]=a,delete t[o])}return t},getItemField:function(t,e,i){var n=t;if("string"!=typeof e||t.hasOwnProperty(e)){return i?this.escapeHTML(t[e]):t[e]}var o,a=e.split("."),s=v(a);try{for(s.s();!(o=s.n()).done;){var r=o.value;n=n&&n[r]}}catch(l){s.e(l)}finally{s.f()}return i?this.escapeHTML(n):n},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=v(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e)){return t.indexOf(o)}}}catch(a){n.e(a)}finally{n.f()}return -1},trToData:function(t,e){var i=this,n=[],o=[];return e.each(function(e,a){var s=y["default"](a),r={};r._id=s.attr("id"),r._class=s.attr("class"),r._data=i.getRealDataAttr(s.data()),r._style=s.attr("style"),s.find(">td,>th").each(function(n,a){for(var s=y["default"](a),l=+s.attr("colspan")||1,c=+s.attr("rowspan")||1,h=n;o[e]&&o[e][h];h++){}for(var u=h;h+l>u;u++){for(var d=e;e+c>d;d++){o[d]||(o[d]=[]),o[d][u]=!0}}var f=t[h].field;r[f]=s.html().trim(),r["_".concat(f,"_id")]=s.attr("id"),r["_".concat(f,"_class")]=s.attr("class"),r["_".concat(f,"_rowspan")]=s.attr("rowspan"),r["_".concat(f,"_colspan")]=s.attr("colspan"),r["_".concat(f,"_title")]=s.attr("title"),r["_".concat(f,"_data")]=i.getRealDataAttr(s.data()),r["_".concat(f,"_style")]=s.attr("style")}),n.push(r)}),n},sort:function(t,e,i,n,o,a){return(void 0===t||null===t)&&(t=""),(void 0===e||null===e)&&(e=""),n&&t===e&&(t=o,e=a),this.isNumeric(t)&&this.isNumeric(e)?(t=parseFloat(t),e=parseFloat(e),e>t?-1*i:t>e?i:0):t===e?0:("string"!=typeof t&&(t=""+t),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1000000*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=v(t);try{for(i.s();!(e=i.n()).done;){for(var n=e.value,o=0,a=Object.keys(n);o<a.length;o++){var s=a[o];if(s.startsWith("_")&&(s.endsWith("_rowspan")||s.endsWith("_colspan"))){return !0}}}}catch(r){i.e(r)}finally{i.f()}return !1},deepCopy:function(t){return void 0===t?t:y["default"].extend(!0,Array.isArray(t)?[]:{},t)}},rs=50,ls=4,cs=function(){function t(e){var i=this;n(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var o=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback(i.startIndex,i.endIndex))};this.scrollEl.addEventListener("scroll",o,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",o,!1)}}return a(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),a=this.checkChanges("top",i.topOffset),s=this.checkChanges("bottom",i.bottomOffset),r=[];o&&a?(i.topOffset&&r.push(this.getExtra("top",i.topOffset)),r.push(n),i.bottomOffset&&r.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=r.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):s&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=this.itemHeight*rs,this.clusterRows=rs*ls,this.clusterHeight=this.blockHeight*ls}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<rs){return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t}}var i=Math.max((this.clusterRows-rs)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),a=Math.max((t.length-n)*this.itemHeight,0),s=[],r=i;1>o&&r++;for(var l=i;n>l;l++){t[l]&&s.push(t[l])}return{start:i,end:n,topOffset:o,bottomOffset:a,rowsAbove:r,rows:s}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),hs=function(){function e(t,i){n(this,e),this.options=i,this.$el=y["default"](t),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return a(e,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var t=this.options;this.constants=Ka.CONSTANTS,this.constants.theme=y["default"].fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var e=t.buttonsPrefix?"".concat(t.buttonsPrefix,"-"):"";this.constants.buttonsClass=[t.buttonsPrefix,e+t.buttonsClass,ss.sprintf("".concat(e,"%s"),t.iconSize)].join(" ").trim(),this.buttons=ss.calculateObjectValue(this,t.buttons,[],{}),"object"!==i(this.buttons)&&(this.buttons={}),"string"==typeof t.icons&&(t.icons=ss.calculateObjectValue(null,t.icons))}},{key:"initLocale",value:function(){if(this.options.locale){var t=y["default"].fn.bootstrapTable.locales,i=this.options.locale.split(/-|_/);i[0]=i[0].toLowerCase(),i[1]&&(i[1]=i[1].toUpperCase());var n={};t[this.options.locale]?n=t[this.options.locale]:t[i.join("-")]?n=t[i.join("-")]:t[i[0]]&&(n=t[i[0]]);for(var o=0,a=Object.entries(n);o<a.length;o++){var r=s(a[o],2),l=r[0],c=r[1];this.options[l]===e.DEFAULTS[l]&&(this.options[l]=c)}}}},{key:"initContainer",value:function(){var t=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",e=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",i=ss.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=y["default"]('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(t,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(i,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(e,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=y["default"]("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var t=this,i=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=y["default"]('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each(function(e,n){var o=y["default"](n),a=[];o.find("th").each(function(t,e){var i=y["default"](e);void 0!==i.data("field")&&i.data("field","".concat(i.data("field"))),a.push(y["default"].extend({},{title:i.html(),"class":i.attr("class"),titleTooltip:i.attr("title"),rowspan:i.attr("rowspan")?+i.attr("rowspan"):void 0,colspan:i.attr("colspan")?+i.attr("colspan"):void 0},i.data()))}),i.push(a),o.attr("class")&&t._headerTrClasses.push(o.attr("class")),o.attr("style")&&t._headerTrStyles.push(o.attr("style"))}),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=y["default"].extend(!0,[],i,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],ss.setFieldIndex(this.options.columns),this.options.columns.forEach(function(i,n){i.forEach(function(i,o){var a=y["default"].extend({},e.COLUMN_DEFAULTS,i);void 0!==a.fieldIndex&&(t.columns[a.fieldIndex]=a,t.fieldsColumnsIndex[a.field]=a.fieldIndex),t.options.columns[n][o]=a})}),!this.options.data.length){var n=ss.trToData(this.columns,this.$el.find(">tbody>tr"));n.length&&(this.options.data=n,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=ss.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var t=this,e={},i=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},ss.updateFieldGroup(this.options.columns),this.options.columns.forEach(function(n,o){var a=[];a.push("<tr".concat(ss.sprintf(' class="%s"',t._headerTrClasses[o])," ").concat(ss.sprintf(' style="%s"',t._headerTrStyles[o]),">"));var r="";if(0===o&&ss.hasDetailViewIcon(t.options)){var l=t.options.columns.length>1?' rowspan="'.concat(t.options.columns.length,'"'):"";r='<th class="detail"'.concat(l,'>\n          <div class="fht-cell"></div>\n          </th>')}r&&"right"!==t.options.detailViewAlign&&a.push(r),n.forEach(function(i,n){var r=ss.sprintf(' class="%s"',i["class"]),l=i.widthUnit,c=parseFloat(i.width),h=ss.sprintf("text-align: %s; ",i.halign?i.halign:i.align),u=ss.sprintf("text-align: %s; ",i.align),d=ss.sprintf("vertical-align: %s; ",i.valign);if(d+=ss.sprintf("width: %s; ",!i.checkbox&&!i.radio||c?c?c+l:void 0:i.showSelectTitle?void 0:"36px"),void 0!==i.fieldIndex||i.visible){var f=ss.calculateObjectValue(null,t.options.headerStyle,[i]),p=[],g="";if(f&&f.css){for(var v=0,b=Object.entries(f.css);v<b.length;v++){var m=s(b[v],2),y=m[0],w=m[1];p.push("".concat(y,": ").concat(w))}}if(f&&f.classes&&(g=ss.sprintf(' class="%s"',i["class"]?[i["class"],f.classes].join(" "):f.classes)),void 0!==i.fieldIndex){if(t.header.fields[i.fieldIndex]=i.field,t.header.styles[i.fieldIndex]=u+d,t.header.classes[i.fieldIndex]=r,t.header.formatters[i.fieldIndex]=i.formatter,t.header.detailFormatters[i.fieldIndex]=i.detailFormatter,t.header.events[i.fieldIndex]=i.events,t.header.sorters[i.fieldIndex]=i.sorter,t.header.sortNames[i.fieldIndex]=i.sortName,t.header.cellStyles[i.fieldIndex]=i.cellStyle,t.header.searchables[i.fieldIndex]=i.searchable,!i.visible){return}if(t.options.cardView&&!i.cardVisible){return}e[i.field]=i}a.push("<th".concat(ss.sprintf(' title="%s"',i.titleTooltip)),i.checkbox||i.radio?ss.sprintf(' class="bs-checkbox %s"',i["class"]||""):g||r,ss.sprintf(' style="%s"',h+d+p.join("; ")),ss.sprintf(' rowspan="%s"',i.rowspan),ss.sprintf(' colspan="%s"',i.colspan),ss.sprintf(' data-field="%s"',i.field),0===n&&o>0?" data-not-first-th":"",">"),a.push(ss.sprintf('<div class="th-inner %s">',t.options.sortable&&i.sortable?"sortable both":""));var S=t.options.escape?ss.escapeHTML(i.title):i.title,x=S;i.checkbox&&(S="",!t.options.singleSelect&&t.options.checkboxHeader&&(S='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),t.header.stateField=i.field),i.radio&&(S="",t.header.stateField=i.field),!S&&i.showSelectTitle&&(S+=x),a.push(S),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</th>")}}),r&&"right"===t.options.detailViewAlign&&a.push(r),a.push("</tr>"),a.length>3&&i.push(a.join(""))}),this.$header.html(i.join("")),this.$header.find("th[data-field]").each(function(t,i){y["default"](i).data(e[y["default"](i).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(e){var i=y["default"](e.currentTarget);return t.options.detailView&&!i.parent().hasClass("bs-checkbox")&&i.closest(".bootstrap-table")[0]!==t.$container[0]?!1:void (t.options.sortable&&i.parent().data().sortable&&t.onSort(e))});var n=ss.getEventName("resize.bootstrap-table",this.$el.attr("id"));y["default"](window).off(n),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),y["default"](window).on(n,function(){return t.resetView()})),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(e){e.stopPropagation();var i=y["default"](e.currentTarget).prop("checked");t[i?"checkAll":"uncheckAll"](),t.updateSelected()})}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||ss.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=r(this.options.data),this.options.sortReset&&(this.unsortedData=r(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach(function(t,e){t.hasOwnProperty("_position")||(t._position=e)}),this.options.customSort?ss.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort(function(o,a){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var s=ss.getItemField(o,e,t.options.escape),r=ss.getItemField(a,e,t.options.escape),l=ss.calculateObjectValue(t.header,t.header.sorters[n],[s,r,o,a]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-a._position):i*l:ss.sort(s,r,i,t.options.sortStable,o._position,a._position)}),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout(function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)},250))):this.options.sortReset&&(this.data=r(this.unsortedData))}},{key:"onSort",value:function(t){var e=t.type,i=t.currentTarget,n="keypress"===e?y["default"](i):y["default"](i).parent(),o=this.$header.find("th").eq(n.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===n.data("field")){var a=this.options.sortOrder;void 0===a?this.options.sortOrder="asc":"asc"===a?this.options.sortOrder="desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else{this.options.sortName=n.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===n.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[n.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[n.data("field")]].order}return this.trigger("sort",this.options.sortName,this.options.sortOrder),n.add(o).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort?(this.options.pageNumber=1,void this.initServer(this.options.silentSort)):(this.initSort(),void this.initBody())}},{key:"initToolbar",value:function(){var t,e=this,n=this.options,o=[],a=0,r=0;this.$toolbar.find(".bs-bars").children().length&&y["default"]("body").append(y["default"](n.toolbar)),this.$toolbar.html(""),("string"==typeof n.toolbar||"object"===i(n.toolbar))&&y["default"](ss.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,n.toolbarAlign)).appendTo(this.$toolbar).append(y["default"](n.toolbar)),o=['<div class="'.concat(["columns","columns-".concat(n.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(n.buttonsAlign)].join(" "),'">')],"string"==typeof n.buttonsOrder&&(n.buttonsOrder=n.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{search:{text:n.formatSearch(),icon:n.icons.search,render:!1,event:this.toggleShowSearch,attributes:{"aria-label":n.formatShowSearch(),title:n.formatShowSearch()}},paginationSwitch:{text:n.pagination?n.formatPaginationSwitchUp():n.formatPaginationSwitchDown(),icon:n.pagination?n.icons.paginationSwitchDown:n.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":n.formatPaginationSwitch(),title:n.formatPaginationSwitch()}},refresh:{text:n.formatRefresh(),icon:n.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":n.formatRefresh(),title:n.formatRefresh()}},toggle:{text:n.formatToggle(),icon:n.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":n.formatToggleOn(),title:n.formatToggleOn()}},fullscreen:{text:n.formatFullscreen(),icon:n.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":n.formatFullscreen(),title:n.formatFullscreen()}},columns:{render:!1,html:function X(){var X=[];if(X.push('<div class="keep-open '.concat(e.constants.classes.buttonsDropdown,'" title="').concat(n.formatColumns(),'">\n            <button class="').concat(e.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(e.constants.dataToggle,'="dropdown"\n            aria-label="Columns" title="').concat(n.formatColumns(),'">\n            ').concat(n.showButtonIcons?ss.sprintf(e.constants.html.icon,n.iconsPrefix,n.icons.columns):"","\n            ").concat(n.showButtonText?n.formatColumns():"","\n            ").concat(e.constants.html.dropdownCaret,"\n            </button>\n            ").concat(e.constants.html.toolbarDropdown[0])),n.showColumnsSearch&&(X.push(ss.sprintf(e.constants.html.toolbarDropdownItem,ss.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',e.constants.classes.input,n.formatSearch()))),X.push(e.constants.html.toolbarDropdownSeparator)),n.showColumnsToggleAll){var t=e.getVisibleColumns().length===e.columns.filter(function(t){return !e.isSelectionColumn(t)}).length;X.push(ss.sprintf(e.constants.html.toolbarDropdownItem,ss.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',t?'checked="checked"':"",n.formatColumnsToggleAll()))),X.push(e.constants.html.toolbarDropdownSeparator)}var i=0;return e.columns.forEach(function(t){t.visible&&i++}),e.columns.forEach(function(t,o){if(!e.isSelectionColumn(t)&&(!n.cardView||t.cardVisible)&&!t.ignore){var a=t.visible?' checked="checked"':"",s=i<=n.minimumCountColumns&&a?' disabled="disabled"':"";t.switchable&&(X.push(ss.sprintf(e.constants.html.toolbarDropdownItem,ss.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',t.field,o,a,s,t.title))),r++)}}),X.push(e.constants.html.toolbarDropdown[1],"</div>"),X.join("")}}});for(var l={},c=0,h=Object.entries(this.buttons);c<h.length;c++){var u=s(h[c],2),d=u[0],f=u[1],p=void 0;if(f.hasOwnProperty("html")){"function"==typeof f.html?p=f.html():"string"==typeof f.html&&(p=f.html)}else{if(p='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(d,'"'),f.hasOwnProperty("attributes")){for(var g=0,b=Object.entries(f.attributes);g<b.length;g++){var m=s(b[g],2),w=m[0],S=m[1];p+=" ".concat(w,'="').concat(S,'"')}}p+=">",n.showButtonIcons&&f.hasOwnProperty("icon")&&(p+="".concat(ss.sprintf(this.constants.html.icon,n.iconsPrefix,f.icon)," ")),n.showButtonText&&f.hasOwnProperty("text")&&(p+=f.text),p+="</button>"}l[d]=p;var x="show".concat(d.charAt(0).toUpperCase()).concat(d.substring(1)),k=n[x];!(!f.hasOwnProperty("render")||f.hasOwnProperty("render")&&f.render)||void 0!==k&&k!==!0||(n[x]=!0),n.buttonsOrder.includes(d)||n.buttonsOrder.push(d)}var O,T=v(n.buttonsOrder);try{for(T.s();!(O=T.n()).done;){var C=O.value,P=n["show".concat(C.charAt(0).toUpperCase()).concat(C.substring(1))];P&&o.push(l[C])}}catch(I){T.e(I)}finally{T.f()}o.push("</div>"),(this.showToolbar||o.length>2)&&this.$toolbar.append(o.join("")),n.showSearch&&this.$toolbar.find('button[name="showSearch"]').off("click").on("click",function(){return e.toggleShowSearch()});for(var A=0,$=Object.entries(this.buttons);A<$.length;A++){var R=s($[A],2),E=R[0],j=R[1];if(j.hasOwnProperty("event")){if("function"==typeof j.event||"string"==typeof j.event){var _=function(){var t="string"==typeof j.event?window[j.event]:j.event;return e.$toolbar.find('button[name="'.concat(E,'"]')).off("click").on("click",function(){return t.call(e)}),"continue"}();if("continue"===_){continue}}for(var N=function(){var t=s(D[F],2),i=t[0],n=t[1],o="string"==typeof n?window[n]:n;e.$toolbar.find('button[name="'.concat(E,'"]')).off(i).on(i,function(){return o.call(e)})},F=0,D=Object.entries(j.event);F<D.length;F++){N()}}}if(n.showColumns){t=this.$toolbar.find(".keep-open");var V=t.find('input[type="checkbox"]:not(".toggle-all")'),B=t.find('input[type="checkbox"].toggle-all');if(r<=n.minimumCountColumns&&t.find("input").prop("disabled",!0),t.find("li, label").off("click").on("click",function(t){t.stopImmediatePropagation()}),V.off("click").on("click",function(t){var i=t.currentTarget,n=y["default"](i);e._toggleColumn(n.val(),n.prop("checked"),!1),e.trigger("column-switch",n.data("field"),n.prop("checked")),B.prop("checked",V.filter(":checked").length===e.columns.filter(function(t){return !e.isSelectionColumn(t)}).length)}),B.off("click").on("click",function(t){var i=t.currentTarget;e._toggleAllColumns(y["default"](i).prop("checked"))}),n.showColumnsSearch){var L=t.find('[name="columnsSearch"]'),H=t.find(".dropdown-item-marker");L.on("keyup paste change",function(t){var e=t.currentTarget,i=y["default"](e),n=i.val().toLowerCase();H.show(),V.each(function(t,e){var i=y["default"](e),o=i.parents(".dropdown-item-marker"),a=o.text().toLowerCase();a.includes(n)||o.hide()})})}}var M=function(t){var i="keyup drop blur mouseup";t.off(i).on(i,function(t){n.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(a),a=setTimeout(function(){e.onSearch({currentTarget:t.currentTarget})},n.searchTimeOut))})};if((n.search||this.showSearchClearButton)&&"string"!=typeof n.searchSelector){o=[];var U=ss.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,n.formatSearch(),n.showButtonIcons?ss.sprintf(this.constants.html.icon,n.iconsPrefix,n.icons.search):"",n.showButtonText?n.formatSearch():""),q=ss.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,n.formatClearSearch(),n.showButtonIcons?ss.sprintf(this.constants.html.icon,n.iconsPrefix,n.icons.clearSearch):"",n.showButtonText?n.formatClearSearch():""),z='<input class="'.concat(this.constants.classes.input,"\n        ").concat(ss.sprintf(" %s%s",this.constants.classes.inputPrefix,n.iconSize),'\n        search-input" type="search" placeholder="').concat(n.formatSearch(),'" autocomplete="off">'),W=z;if(n.showSearchButton||n.showSearchClearButton){var G=(n.showSearchButton?U:"")+(n.showSearchClearButton?q:"");W=n.search?ss.sprintf(this.constants.html.inputGroup,z,G):G}o.push(ss.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(n.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),W)),this.$toolbar.append(o.join(""));var K=ss.getSearchInput(this);n.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",function(){clearTimeout(a),a=setTimeout(function(){e.onSearch({currentTarget:K})},n.searchTimeOut)}),n.searchOnEnterKey&&M(K)):M(K),n.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click(function(){e.resetSearch()})}else{if("string"==typeof n.searchSelector){var Y=ss.getSearchInput(this);M(Y)}}}},{key:"onSearch",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.currentTarget,i=t.firedByInitSearchText,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:!0;if(void 0!==e&&y["default"](e).length&&n){var o=y["default"](e).val().trim();if(this.options.trimOnSearch&&y["default"](e).val()!==o&&y["default"](e).val(o),this.searchText===o){return}var a=ss.getSearchInput(this),s=e instanceof jQuery?e:y["default"](e);(s.is(a)||s.hasClass("search-input"))&&(this.searchText=o,this.options.searchText=o)}i||(this.options.pageNumber=1),this.initSearch(),i?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch){return this.data=ss.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),void (this.options.sortReset&&(this.unsortedData=r(this.data)))}var e=this.searchText&&(this.fromHtml?ss.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=ss.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=ss.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter(function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])}):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter(function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n){if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o]){return !1}}}else{if("or"===i){var a=!1;for(var s in n){(Array.isArray(n[s])&&n[s].includes(e[s])||!Array.isArray(n[s])&&e[s]===n[s])&&(a=!0)}return a}}return !0}):r(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter(function(n,a){for(var s=0;s<t.header.fields.length;s++){if(t.header.searchables[s]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[s]))){var r=ss.isNumeric(t.header.fields[s])?parseInt(t.header.fields[s],10):t.header.fields[s],l=t.columns[t.fieldsColumnsIndex[r]],c=void 0;if("string"==typeof r){c=n;for(var h=r.split("."),u=0;u<h.length;u++){null!==c[h[u]]&&(c=c[h[u]])}}else{c=n[r]}if(t.options.searchAccentNeutralise&&(c=ss.normalizeAccent(c)),l&&l.searchFormatter&&(c=ss.calculateObjectValue(l,t.header.formatters[s],[c,n,a,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&ss.regexCompare(c,e)){return !0}var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm,f=d.exec(t.searchText),p=!1;if(f){var g=f[1]||"".concat(f[5],"l"),v=f[2]||f[3],b=parseInt(c,10),m=parseInt(v,10);switch(g){case">":case"<l":p=b>m;break;case"<":case">l":p=m>b;break;case"<=":case"=<":case">=l":case"=>l":p=m>=b;break;case">=":case"=>":case"<=l":case"=<l":p=b>=m}}if(p||"".concat(c).toLowerCase().includes(i)){return !0}}}}return !1}):this.data,this.options.sortReset&&(this.unsortedData=r(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var e=this,i=this.options;if(!i.pagination){return void this.$pagination.hide()}this.$pagination.show();var n,o,a,s,r,l,c,h=[],u=!1,d=this.getData({includeHiddenRows:!1}),f=i.pageList;if("string"==typeof f&&(f=f.replace(/\[|\]| /g,"").toLowerCase().split(",")),f=f.map(function(t){return"string"==typeof t?t.toLowerCase()===i.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?i.formatAllRows():+t:t}),this.paginationParts=i.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==i.sidePagination&&(i.totalRows=d.length),this.totalPages=0,i.totalRows&&(i.pageSize===i.formatAllRows()&&(i.pageSize=i.totalRows,u=!0),this.totalPages=~~((i.totalRows-1)/i.pageSize)+1,i.totalPages=this.totalPages),this.totalPages>0&&i.pageNumber>this.totalPages&&(i.pageNumber=this.totalPages),this.pageFrom=(i.pageNumber-1)*i.pageSize+1,this.pageTo=i.pageNumber*i.pageSize,this.pageTo>i.totalRows&&(this.pageTo=i.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&h.push('<div class="'.concat(this.constants.classes.pull,"-").concat(i.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var p=this.paginationParts.includes("pageInfoShort")?i.formatDetailPagination(i.totalRows):i.formatShowingRows(this.pageFrom,this.pageTo,i.totalRows,i.totalNotFiltered);h.push('<span class="pagination-info">\n      '.concat(p,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){h.push('<div class="page-list">');var g=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(u?i.formatAllRows():i.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];f.forEach(function(t,n){if(!i.smartDisplay||0===n||f[n-1]<i.totalRows||t===i.formatAllRows()){var o;o=u?t===i.formatAllRows()?e.constants.classes.dropdownActive:"":t===i.pageSize?e.constants.classes.dropdownActive:"",g.push(ss.sprintf(e.constants.html.pageDropdownItem,o,t))}}),g.push("".concat(this.constants.html.pageDropdown[1],"</div>")),h.push(i.formatRecordsPerPage(g.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&h.push("</div></div>"),this.paginationParts.includes("pageList")){h.push('<div class="'.concat(this.constants.classes.pull,"-").concat(i.paginationHAlign,' pagination">'),ss.sprintf(this.constants.html.pagination[0],ss.sprintf(" pagination-%s",i.iconSize)),ss.sprintf(this.constants.html.paginationItem," page-pre",i.formatSRPaginationPreText(),i.paginationPreText)),this.totalPages<i.paginationSuccessivelySize?(o=1,a=this.totalPages):(o=i.pageNumber-i.paginationPagesBySide,a=o+2*i.paginationPagesBySide),i.pageNumber<i.paginationSuccessivelySize-1&&(a=i.paginationSuccessivelySize),i.paginationSuccessivelySize>this.totalPages-o&&(o=o-(i.paginationSuccessivelySize-(this.totalPages-o))+1),1>o&&(o=1),a>this.totalPages&&(a=this.totalPages);var v=Math.round(i.paginationPagesBySide/2),b=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return ss.sprintf(e.constants.html.paginationItem,n+(t===i.pageNumber?" ".concat(e.constants.classes.paginationActive):""),i.formatSRPaginationPageText(t),t)};if(o>1){var m=i.paginationPagesBySide;for(m>=o&&(m=o-1),n=1;m>=n;n++){h.push(b(n))}o-1===m+1?(n=o-1,h.push(b(n))):o-1>m&&(o-2*i.paginationPagesBySide>i.paginationPagesBySide&&i.paginationUseIntermediate?(n=Math.round((o-v)/2+v),h.push(b(n," page-intermediate"))):h.push(ss.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(n=o;a>=n;n++){h.push(b(n))}if(this.totalPages>a){var y=this.totalPages-(i.paginationPagesBySide-1);for(a>=y&&(y=a+1),a+1===y-1?(n=a+1,h.push(b(n))):y>a+1&&(this.totalPages-a>2*i.paginationPagesBySide&&i.paginationUseIntermediate?(n=Math.round((this.totalPages-v-a)/2+a),h.push(b(n," page-intermediate"))):h.push(ss.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),n=y;n<=this.totalPages;n++){h.push(b(n))}}h.push(ss.sprintf(this.constants.html.paginationItem," page-next",i.formatSRPaginationNextText(),i.paginationNextText)),h.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(h.join(""));var w=["bottom","both"].includes(i.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";if(this.$pagination.last().find(".page-list > div").addClass(w),!i.onlyInfoPagination&&(s=this.$pagination.find(".page-list a"),r=this.$pagination.find(".page-pre"),l=this.$pagination.find(".page-next"),c=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),i.smartDisplay&&(f.length<2||i.totalRows<=f[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),i.paginationLoop||(1===i.pageNumber&&r.addClass("disabled"),i.pageNumber===this.totalPages&&l.addClass("disabled")),u&&(i.pageSize=i.formatAllRows()),s.off("click").on("click",function(t){return e.onPageListChange(t)}),r.off("click").on("click",function(t){return e.onPagePre(t)}),l.off("click").on("click",function(t){return e.onPageNext(t)}),c.off("click").on("click",function(t){return e.onPageNumber(t)}),this.options.showPageGo)){var S=this,x=this.$pagination.find("ul.pagination"),k=x.find("li.pageGo");k.length||(k=t('<li class="pageGo">'+ss.sprintf('<input type="text" class="form-control" value="%s">',this.options.pageNumber)+('<button class="btn'+ss.sprintf(" btn-%s",this.constants.buttonsClass)+ss.sprintf(" btn-%s",i.iconSize)+'" title="'+i.formatPageGo()+'"  type="button">'+i.formatPageGo())+"</button></li>").appendTo(x),k.find("button").click(function(){var t=parseInt(k.find("input").val())||1;(1>t||t>S.options.totalPages)&&(t=1),S.selectPage(t)}))}}},{key:"updatePagination",value:function(t){t&&y["default"](t.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(t){t.preventDefault();var e=y["default"](t.currentTarget);return e.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=e.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+e.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(t),!1}},{key:"onPagePre",value:function(t){return y["default"](t.target).hasClass("disabled")?void 0:(t.preventDefault(),this.options.pageNumber-1===0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1)}},{key:"onPageNext",value:function(t){return y["default"](t.target).hasClass("disabled")?void 0:(t.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1)}},{key:"onPageNumber",value:function(t){return t.preventDefault(),this.options.pageNumber!==+y["default"](t.currentTarget).text()?(this.options.pageNumber=+y["default"](t.currentTarget).text(),this.updatePagination(t),!1):void 0}},{key:"initRow",value:function(t,e,n,o){var a=this,r=[],l={},c=[],h="",u={},d=[];if(!(ss.findIndex(this.hiddenRows,t)>-1)){if(l=ss.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l),l&&l.css){for(var f=0,p=Object.entries(l.css);f<p.length;f++){var g=s(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}}if(u=ss.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u)){for(var m=0,y=Object.entries(u);m<y.length;m++){var w=s(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(ss.escapeHTML(x),'"'))}}if(t._data&&!ss.isEmptyObject(t._data)){for(var k=0,O=Object.entries(t._data);k<O.length;k++){var T=s(O[k],2),C=T[0],P=T[1];if("index"===C){return}h+=" data-".concat(C,"='").concat("object"===i(P)?JSON.stringify(P):P,"'")}}r.push("<tr",ss.sprintf(" %s",d.length?d.join(" "):void 0),ss.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),ss.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),ss.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),ss.sprintf(' data-uniqueid="%s"',ss.getItemField(t,this.options.uniqueId,!1)),ss.sprintf(' data-has-detail-view="%s"',this.options.detailView&&ss.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),ss.sprintf("%s",h),">"),this.options.cardView&&r.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";return ss.hasDetailViewIcon(this.options)&&(I="<td>",ss.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(I+='\n          <a class="detail-icon" href="#">\n          '.concat(ss.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),I+="</td>"),I&&"right"!==this.options.detailViewAlign&&r.push(I),this.header.fields.forEach(function(i,n){var o="",l=ss.getItemField(t,i,a.options.escape),h="",u="",d={},f="",p=a.header.classes[n],g="",v="",b="",m="",y="",w="",S=a.columns[n];if((!a.fromHtml&&!a.autoMergeCells||void 0!==l||S.checkbox||S.radio)&&S.visible&&(!a.options.cardView||S.cardVisible)){if(S.escape&&(l=ss.escapeHTML(l)),c.concat([a.header.styles[n]]).length&&(v+="".concat(c.concat([a.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(v+="".concat(t["_".concat(i,"_style")])),v&&(g=' style="'.concat(v,'"')),t["_".concat(i,"_id")]&&(f=ss.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(p=ss.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(m=ss.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(y=ss.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(w=ss.sprintf(' title="%s"',t["_".concat(i,"_title")])),d=ss.calculateObjectValue(a.header,a.header.cellStyles[n],[l,t,e,i],d),d.classes&&(p=' class="'.concat(d.classes,'"')),d.css){for(var x=[],k=0,O=Object.entries(d.css);k<O.length;k++){var T=s(O[k],2),C=T[0],P=T[1];x.push("".concat(C,": ").concat(P))}g=' style="'.concat(x.concat(a.header.styles[n]).join("; "),'"')}if(h=ss.calculateObjectValue(S,a.header.formatters[n],[l,t,e,i],l),S.checkbox||S.radio||(h=void 0===h||null===h?a.options.undefinedText:h),S.searchable&&a.searchText&&a.options.searchHighlight&&!S.checkbox&&!S.radio){var I="",A=RegExp("(".concat(a.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),")"),"gim"),$="<mark>$1</mark>",R=h&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(h);if(R){var E=(new DOMParser).parseFromString(""+h,"text/html").documentElement.textContent,j=E.replace(A,$);E=E.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),I=h.replace(RegExp("(>\\s*)(".concat(E,")(\\s*)"),"gm"),"$1".concat(j,"$3"))}else{I=(""+h).replace(A,$)}h=ss.calculateObjectValue(S,S.searchHighlightFormatter,[h,a.searchText],I)}if(t["_".concat(i,"_data")]&&!ss.isEmptyObject(t["_".concat(i,"_data")])){for(var _=0,N=Object.entries(t["_".concat(i,"_data")]);_<N.length;_++){var F=s(N[_],2),D=F[0],V=F[1];if("index"===D){return}b+=" data-".concat(D,'="').concat(V,'"')}}if(S.checkbox||S.radio){u=S.checkbox?"checkbox":u,u=S.radio?"radio":u;var B=S["class"]||"",L=ss.isObject(h)&&h.hasOwnProperty("checked")?h.checked:(h===!0||l)&&h!==!1,H=!S.checkboxEnabled||h&&h.disabled;o=""+(a.options.cardView?'<div class="card-view '.concat(B,'">'):'<td class="bs-checkbox '.concat(B,'"').concat(p).concat(g,">"))+'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(a.options.selectItemName,'"\n            type="').concat(u,'"\n            ').concat(ss.sprintf('value="%s"',t[a.options.idField]),"\n            ").concat(ss.sprintf('checked="%s"',L?"checked":void 0),"\n            ").concat(ss.sprintf('disabled="%s"',H?"disabled":void 0)," />\n            <span></span>\n            </label>")+(a.header.formatters[n]&&"string"==typeof h?h:"")+(a.options.cardView?"</div>":"</td>"),t[a.header.stateField]=h===!0||!!l||h&&h.checked}else{if(a.options.cardView){var M=a.options.showHeader?'<span class="card-view-title '.concat(d.classes,'"').concat(g,">").concat(ss.getFieldTitle(a.columns,i),"</span>"):"";o='<div class="card-view">'.concat(M,'<span class="card-view-value ').concat(d.classes,'"').concat(g,">").concat(h,"</span></div>"),a.options.smartDisplay&&""===h&&(o='<div class="card-view"></div>')}else{o="<td".concat(f).concat(p).concat(g).concat(b).concat(m).concat(y).concat(w,">").concat(h,"</td>")}}r.push(o)}}),I&&"right"===this.options.detailViewAlign&&r.push(I),this.options.cardView&&r.push("</div></td>"),r.push("</tr>"),r.join("")}}},{key:"initBody",value:function(t,e){var i=this,n=this.getData();this.trigger("pre-body",n),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=y["default"]("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=n.length);var o=[],a=y["default"](document.createDocumentFragment()),s=!1,r=[];this.autoMergeCells=ss.checkAutoMergeCells(n.slice(this.pageFrom-1,this.pageTo));for(var l=this.pageFrom-1;l<this.pageTo;l++){var c=n[l],h=this.initRow(c,l,n,a);if(s=s||!!h,h&&"string"==typeof h){var u=this.options.uniqueId;if(u&&c.hasOwnProperty(u)){var d=c[u],f=this.$body.find(ss.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',d)),p=f.next();p.is("tr.detail-view")&&(r.push(l),e&&d===e||(h+=p[0].outerHTML))}this.options.virtualScroll?o.push(h):a.append(h)}}s?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new cs({rows:o,fixedScroll:t,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){i.fitHeader(),i.initBodyEvent(),i.trigger("virtual-scroll",t,e)}})):this.$body.html(a):this.$body.html('<tr class="no-records-found">'.concat(ss.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+ss.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),r.forEach(function(t){i.expandRow(t)}),t||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=n.length),this.trigger("post-body",n)}},{key:"initBodyEvent",value:function(){var t=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var i=y["default"](e.currentTarget),n=i.parent(),o=y["default"](e.target).parents(".card-views").children(),a=y["default"](e.target).parents(".card-view"),s=n.data("index"),r=t.data[s],l=t.options.cardView?o.index(a):i[0].cellIndex,c=t.getVisibleFields(),h=c[l-ss.getDetailViewIndexOffset(t.options)],u=t.columns[t.fieldsColumnsIndex[h]],d=ss.getItemField(r,h,t.options.escape);if(!i.find(".detail-icon").length){if(t.trigger("click"===e.type?"click-cell":"dbl-click-cell",h,d,r,i),t.trigger("click"===e.type?"click-row":"dbl-click-row",r,n,h),"click"===e.type&&t.options.clickToSelect&&u.clickToSelect&&!ss.calculateObjectValue(t.options,t.options.ignoreClickToSelectOn,[e.target])){var f=n.find(ss.sprintf('[name="%s"]',t.options.selectItemName));f.length&&f[0].click()}"click"===e.type&&t.options.detailViewByClick&&t.toggleDetailView(s,t.header.detailFormatters[t.fieldsColumnsIndex[h]])}}).off("mousedown").on("mousedown",function(e){t.multipleSelectRowCtrlKey=e.ctrlKey||e.metaKey,t.multipleSelectRowShiftKey=e.shiftKey}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(e){return e.preventDefault(),t.toggleDetailView(y["default"](e.currentTarget).parent().parent().data("index")),!1}),this.$selectItem=this.$body.find(ss.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(e){e.stopImmediatePropagation();var i=y["default"](e.currentTarget);t._toggleCheck(i.prop("checked"),i.data("index"))}),this.header.events.forEach(function(e,i){var n=e;if(n){"string"==typeof n&&(n=ss.calculateObjectValue(null,n));var o=t.header.fields[i],a=t.getVisibleFields().indexOf(o);if(-1!==a){a+=ss.getDetailViewIndexOffset(t.options);var s=function(e){if(!n.hasOwnProperty(e)){return"continue"}var i=n[e];t.$body.find(">tr:not(.no-records-found)").each(function(n,s){var r=y["default"](s),l=r.find(t.options.cardView?".card-views>.card-view":">td").eq(a),c=e.indexOf(" "),h=e.substring(0,c),u=e.substring(c+1);l.find(u).off(h).on(h,function(e){var n=r.data("index"),a=t.data[n],s=a[o];i.apply(t,[e,s,a,n])})})};for(var r in n){s(r)}}}})}},{key:"initServer",value:function(t,e,i){var n=this,o={},a=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[a]&&(s.sortName=this.header.sortNames[a]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),!this.options.firstLoad&&!firstLoadTable.includes(this.options.id)){return void firstLoadTable.push(this.options.id)}if(i||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize,(0===s.limit||this.options.pageSize===this.options.formatAllRows())&&delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter(function(t){return !t.searchable}).length){s.searchable=[];var r,l=v(this.columns);try{for(l.s();!(r=l.n()).done;){var c=r.value;!c.checkbox&&c.searchable&&(this.options.visibleSearch&&c.visible||!this.options.visibleSearch)&&s.searchable.push(c.field)}}catch(h){l.e(h)}finally{l.f()}}if(ss.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),y["default"].extend(s,e||{}),o=ss.calculateObjectValue(this.options,this.options.queryParams,[s],o),o!==!1){t||this.showLoading();var u=y["default"].extend({},ss.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:i||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(o):o,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(e,i,o){var a=ss.calculateObjectValue(n.options,n.options.responseHandler,[e,o],e);n.load(a),n.trigger("load-success",a,o&&o.status,o),t||n.hideLoading(),"server"===n.options.sidePagination&&n.options.pageNumber>1&&a[n.options.totalField]>0&&!a[n.options.dataField].length&&n.updatePagination()},error:function(e){if(e&&0===e.status&&n._xhrAbort){return void (n._xhrAbort=!1)}var i=[];"server"===n.options.sidePagination&&(i={},i[n.options.totalField]=0,i[n.options.dataField]=[]),n.load(i),n.trigger("load-error",e&&e.status,e),t||n.$tableLoading.hide()}});return this.options.ajax?ss.calculateObjectValue(this,this.options.ajax,[u],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=y["default"].ajax(u)),o}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=ss.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var t=this;this.$header.find("th").each(function(e,i){y["default"](i).find(".sortable").removeClass("desc asc").addClass(y["default"](i).data("field")===t.options.sortName?t.options.sortOrder:"both")})}},{key:"updateSelected",value:function(){var t=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.each(function(t,e){y["default"](e).closest("tr")[y["default"](e).prop("checked")?"addClass":"removeClass"]("selected")})}},{key:"updateRows",value:function(){var t=this;this.$selectItem.each(function(e,i){t.data[y["default"](i).data("index")][t.header.stateField]=y["default"](i).prop("checked")})}},{key:"resetRows",value:function(){var t,e=v(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(n){e.e(n)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(t){for(var i,n,o="".concat(t,".bs.table"),a=arguments.length,s=Array(a>1?a-1:0),r=1;a>r;r++){s[r-1]=arguments[r]}(i=this.options)[e.EVENTS[o]].apply(i,[].concat(s,[this])),this.$el.trigger(y["default"].Event(o,{sender:this}),s),(n=this.options).onAll.apply(n,[o].concat([].concat(s,[this]))),this.$el.trigger(y["default"].Event("all.bs.table",{sender:this}),[o,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(function(){return t.fitHeader()},this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var t=this;if(this.$el.is(":hidden")){return void (this.timeoutId_=setTimeout(function(){return t.fitHeader()},100))}var e=this.$tableBody.get(0),i=e.scrollWidth>e.clientWidth&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?ss.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var n=y["default"](":focus");if(n.length>0){var o=n.parents("th");if(o.length>0){var a=o.attr("data-field");if(void 0!==a){var s=this.$header.find("[data-field='".concat(a,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",i).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var r=y["default"](".focus-temp:visible:eq(0)");r.length>0&&(r.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(e,i){t.$header_.find(ss.sprintf('th[data-field="%s"]',y["default"](i).data("field"))).data(y["default"](i).data())});for(var l=this.getVisibleFields(),c=this.$header_.find("th"),h=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);h.length&&h.find('>td[colspan]:not([colspan="1"])').length;){h=h.next()}var u=h.find("> *").length;h.find("> *").each(function(e,i){var n=y["default"](i);if(ss.hasDetailViewIcon(t.options)&&(0===e&&"right"!==t.options.detailViewAlign||e===u-1&&"right"===t.options.detailViewAlign)){var o=c.filter(".detail"),a=o.innerWidth()-o.find(".fht-cell").width();return void o.find(".fht-cell").width(n.innerWidth()-a)}var s=e-ss.getDetailViewIndexOffset(t.options),r=t.$header_.find(ss.sprintf('th[data-field="%s"]',l[s]));r.length>1&&(r=y["default"](c[n[0].cellIndex]));var h=r.innerWidth()-r.find(".fht-cell").width();r.find(".fht-cell").width(n.innerWidth()-h)}),this.horizontalScroll(),this.trigger("post-header")}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";ss.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=v(this.columns);try{for(o.s();!(n=o.n()).done;){var a=n.value,r="",l="",c=[],h={},u=ss.sprintf(' class="%s"',a["class"]);if(a.visible&&(!(this.footerData&&this.footerData.length>0)||a.field in this.footerData[0])){if(this.options.cardView&&!a.cardVisible){return}if(r=ss.sprintf("text-align: %s; ",a.falign?a.falign:a.align),l=ss.sprintf("vertical-align: %s; ",a.valign),h=ss.calculateObjectValue(null,this.options.footerStyle,[a]),h&&h.css){for(var d=0,f=Object.entries(h.css);d<f.length;d++){var p=s(f[d],2),g=p[0],b=p[1];c.push("".concat(g,": ").concat(b))}}h&&h.classes&&(u=ss.sprintf(' class="%s"',a["class"]?[a["class"],h.classes].join(" "):h.classes)),e.push("<th",u,ss.sprintf(' style="%s"',r+l+c.concat().join("; ")));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(a.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][a.field]||""),e.push(ss.calculateObjectValue(a,a.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(w){o.e(w)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var t=this;if(this.$el.is(":hidden")){return void setTimeout(function(){return t.fitFooter()},100)}var e=this.$tableBody.get(0),i=e.scrollWidth>e.clientWidth&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?ss.getScrollBarWidth():0;this.$tableFooter.css("margin-right",i).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var n=this.$tableFooter.find("th"),o=this.$body.find(">tr:first-child:not(.no-records-found)");for(n.find(".fht-cell").width("auto");o.length&&o.find('>td[colspan]:not([colspan="1"])').length;){o=o.next()}var a=o.find("> *").length;o.find("> *").each(function(e,i){var o=y["default"](i);if(ss.hasDetailViewIcon(t.options)&&(0===e&&"left"===t.options.detailViewAlign||e===a-1&&"right"===t.options.detailViewAlign)){var s=n.filter(".detail"),r=s.innerWidth()-s.find(".fht-cell").width();return void s.find(".fht-cell").width(o.innerWidth()-r)}var l=n.eq(e),c=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-c)}),this.horizontalScroll()}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)})}},{key:"getVisibleFields",value:function(){var t,e=[],i=v(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&e.push(n)}}catch(a){i.e(a)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=y["default"].extend({},this.options);return delete t.data,y["default"].extend(!0,{},t)}},{key:"refreshOptions",value:function(t){ss.compareObjects(this.options,t,!0)||(this.options=y["default"].extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&ss.isEmptyObject(this.filterColumns)&&ss.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter(function(t){return -1===ss.findIndex(n,t)})}return t&&t.formatted&&i.forEach(function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=s(n[i],2),a=o[0],r=o[1],l=e.columns[e.fieldsColumnsIndex[a]];if(!l){return}t[a]=ss.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[r,t,t.index,l.field],r)}}),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter(function(e){return e[t.header.stateField]===!0})}},{key:"load",value:function(t){var e=!1,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i];(n.hasOwnProperty(t.field)||"$index"===t.field)&&(!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(n[t.field]))&&(e++,this.options.data.splice(i,1))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=r(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,i=Array.isArray(t)?t:[t],n=v(i);try{for(n.s();!(e=n.n()).done;){var o=e.value;o.hasOwnProperty("index")&&o.hasOwnProperty("row")&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[o.index]=o.row:y["default"].extend(this.options.data[o.index],o.row))}}catch(a){n.e(a)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n,o=this.options.uniqueId,a=this.options.data.length,s=t,r=null;for(e=a-1;e>=0;e--){if(i=this.options.data[e],i.hasOwnProperty(o)){n=i[o]}else{if(!i._data||!i._data.hasOwnProperty(o)){continue}n=i._data[o]}if("string"==typeof n?s=""+s:"number"==typeof n&&(+n===n&&n%1===0?s=parseInt(s,10):n===+n&&0!==n&&(s=parseFloat(s))),n===s){r=i;break}}return r}},{key:"updateByUniqueId",value:function(t){var e,i=Array.isArray(t)?t:[t],n=null,o=v(i);try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.hasOwnProperty("id")&&a.hasOwnProperty("row")){var s=this.options.data.indexOf(this.getRowByUniqueId(a.id));-1!==s&&(a.hasOwnProperty("replace")&&a.replace?this.options.data[s]=a.row:y["default"].extend(this.options.data[s],a.row),n=a.id)}}}catch(r){o.e(r)}finally{o.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,n)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=r(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,t.reinit!==!1&&(this.initSort(),this.initBody(!0)))}},{key:"updateCellByUniqueId",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){var i=t.id,n=t.field,o=t.value,a=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==a&&(e.options.data[a][n]=o)}),t.reinit!==!1&&(this.initSort(),this.initBody(!0))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=ss.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t){return this.initHiddenRows(),this.initBody(!0),void this.initPagination()}var e,i=this.getData(),n=[],o=v(i);try{for(o.s();!(e=o.n()).done;){var a=e.value;this.hiddenRows.includes(a)&&n.push(a)}}catch(s){o.e(s)}finally{o.f()}return this.hiddenRows=n,n}},{key:"showColumn",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)})}},{key:"hideColumn",value:function(t){var e=this,i=Array.isArray(t)?t:[t];i.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)})}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(ss.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter(function(e){return e.visible&&!t.isSelectionColumn(e)})}},{key:"getHiddenColumns",value:function(){return this.columns.filter(function(t){var e=t.visible;return !e})}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(t){var e,i=this,n=v(this.columns.slice().reverse());try{for(n.s();!(e=n.n()).done;){var o=e.value;if(o.switchable){if(!t&&this.options.showColumns&&this.getVisibleColumns().length===this.options.minimumCountColumns){continue}o.visible=t}}}catch(a){n.e(a)}finally{n.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var s=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);t?s.prop("checked",t):s.get().reverse().forEach(function(e){s.filter(":checked").length>i.options.minimumCountColumns&&y["default"](e).prop("checked",t)}),s.filter(":checked").length<=this.options.minimumCountColumns&&s.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),a=t.rowspan||1,s=t.colspan||1,r=this.$body.find(">tr[data-index]");o+=ss.getDetailViewIndexOffset(this.options);var l=r.eq(n).find(">td").eq(o);if(!(0>n||0>o||n>=this.data.length)){for(e=n;n+a>e;e++){for(i=o;o+s>i;i++){r.eq(e).find(">td").eq(i).hide()}}l.attr("rowspan",a).attr("colspan",s).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();return t?void this.trigger("check-all",i,e):void this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var t=this.$selectItem.filter(":enabled"),e=t.filter(":checked");t.each(function(t,e){y["default"](e).prop("checked",!y["default"](e).prop("checked"))}),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",e),e=this.getSelections(),this.trigger("check-some",e)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,a=v(this.options.data);try{for(a.s();!(o=a.n()).done;){var r=o.value;r[this.header.stateField]=!1}}catch(l){a.e(l)}finally{a.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0){for(var c=this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],h=s(c,2),u=h[0],d=h[1],f=u+1;d>f;f++){this.data[f][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(f,'"]')).prop("checked",!0)}}this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach(function(o,a){if(!o.hasOwnProperty(e.field)){return !1}if(e.values.includes(o[e.field])){var s=i.$selectItem.filter(":enabled").filter(ss.sprintf('[data-index="%s"]',a)),r=e.hasOwnProperty("onlyCurrentPage")?e.onlyCurrentPage:!1;if(s=t?s.not(":checked"):s.filter(":checked"),!s.length&&r){return}s.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,s)}}),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),table.rememberSelecteds={},table.rememberSelectedIds={},this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),y["default"](this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen")){this.$tableContainer.css("height",""),this.$tableContainer.css("width","")}else{if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var i=this.$toolbar.outerHeight(!0),n=this.$pagination.outerHeight(!0),o=this.options.height-i-n,a=this.$tableBody.find(">table"),s=a.outerHeight();if(this.$tableContainer.css("height","".concat(o,"px")),this.$tableBorder&&a.is(":visible")){var r=o-s-2;this.$tableBody[0].scrollWidth-this.$tableBody.innerWidth()&&(r-=ss.getScrollBarWidth()),this.$tableBorder.css("width","".concat(a.outerWidth(),"px")),this.$tableBorder.css("height","".concat(r,"px"))}}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=0.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"toggleShowSearch",value:function(){this.$el.parents(".select-table").siblings().slideToggle()}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(ss.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(ss.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=ss.getSearchInput(this);e.val(t||""),this.onSearch({currentTarget:e})}},{key:"filterBy",value:function(t,e){this.filterOptions=ss.isEmptyObject(e)?this.options.filterOptions:y["default"].extend(this.options.filterOptions,e),this.filterColumns=ss.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function o(t){var e={unit:"px",value:0};"object"===i(t)?e=Object.assign(e,t):"string"==typeof t&&"bottom"===t?e.value=this.$tableBody[0].scrollHeight:("string"==typeof t||"number"==typeof t)&&(e.value=t);var o=e.value;"rows"===e.unit&&(o=0,this.$body.find("> tr:lt(".concat(e.value,")")).each(function(t,e){o+=y["default"](e).outerHeight(!0)})),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){var i=this.$body.find(ss.sprintf('> tr[data-index="%s"]',t));i.next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(ss.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(ss.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(ss.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),a=e||this.options.detailFormatter,s=ss.calculateObjectValue(this.options,a,[t,i,o],"");1===o.length&&o.append(s),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(ss.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(ss.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++){this.expandRow(y["default"](t[e]).data("index"))}}},{key:"collapseAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++){this.collapseRow(y["default"](t[e]).data("index"))}}},{key:"updateColumnTitle",value:function(t){t.hasOwnProperty("field")&&t.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[t.field]].title=this.options.escape?ss.escapeHTML(t.title):t.title,this.columns[this.fieldsColumnsIndex[t.field]].visible&&(this.$header.find("th[data-field]").each(function(e,i){return y["default"](i).data("field")===t.field?(y["default"](y["default"](i).find(".th-inner")[0]).text(t.title),!1):void 0}),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),e}();return hs.VERSION=Ka.VERSION,hs.DEFAULTS=Ka.DEFAULTS,hs.LOCALES=Ka.LOCALES,hs.COLUMN_DEFAULTS=Ka.COLUMN_DEFAULTS,hs.METHODS=Ka.METHODS,hs.EVENTS=Ka.EVENTS,y["default"].BootstrapTable=hs,y["default"].fn.bootstrapTable=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),o=1;e>o;o++){n[o-1]=arguments[o]}var a;return this.each(function(e,o){var s=y["default"](o).data("bootstrap.table"),r=y["default"].extend({},hs.DEFAULTS,y["default"](o).data(),"object"===i(t)&&t);if("string"==typeof t){var l;if(!Ka.METHODS.includes(t)){throw Error("Unknown method: ".concat(t))}if(!s){return}a=(l=s)[t].apply(l,n),"destroy"===t&&y["default"](o).removeData("bootstrap.table")}s||(s=new y["default"].BootstrapTable(o,r),y["default"](o).data("bootstrap.table",s),s.init())}),void 0===a?this:a},y["default"].fn.bootstrapTable.Constructor=hs,y["default"].fn.bootstrapTable.theme=Ka.THEME,y["default"].fn.bootstrapTable.VERSION=Ka.VERSION,y["default"].fn.bootstrapTable.defaults=hs.DEFAULTS,y["default"].fn.bootstrapTable.columnDefaults=hs.COLUMN_DEFAULTS,y["default"].fn.bootstrapTable.events=hs.EVENTS,y["default"].fn.bootstrapTable.locales=hs.LOCALES,y["default"].fn.bootstrapTable.methods=hs.METHODS,y["default"].fn.bootstrapTable.utils=ss,y["default"](function(){y["default"]('[data-toggle="table"]').bootstrapTable()}),hs});var TABLE_EVENTS="all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table",firstLoadTable=[],union=function(t,e){return $.isPlainObject(e)?addRememberRow(t,e):$.isArray(e)?$.each(e,function(e,i){$.isPlainObject(i)?addRememberRow(t,i):-1==$.inArray(i,t)&&(t[t.length]=i)}):-1==$.inArray(e,t)&&(t[t.length]=e),t},difference=function(t,e){if($.isPlainObject(e)){removeRememberRow(t,e)}else{if($.isArray(e)){$.each(e,function(e,i){if($.isPlainObject(i)){removeRememberRow(t,i)}else{var n=$.inArray(i,t);-1!=n&&t.splice(n,1)}})}else{var i=$.inArray(e,t);-1!=i&&t.splice(i,1)}}return t},_={union:union,difference:difference};