<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('组合表头')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 select-table table-bordered">
				<table id="bootstrap-table"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "demo/table";
        var datas = [[${@dict.getType('sys_normal_disable')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        columns : [
        				[{
        					title : '基本信息',
        					align : 'center',
        					colspan : 6
        				}, {
        					title : '其他信息',
        					align : 'center',
        					colspan : 3
        				}
        			],
        			[{
        					checkbox : true
        				}, {
        					field : 'userId',
        					title : '用户ID'
        				}, {
        					field : 'userCode',
        					title : '用户编号'
        				}, {
        					field : 'userName',
        					title : '用户姓名'
        				}, {
        					field : 'userPhone',
        					title : '用户手机'
        				}, {
        					field : 'userEmail',
        					title : '用户邮箱'
        				}, {
        					field : 'userBalance',
        					title : '用户余额'
        				}, {
        					field : 'status',
        					title : '用户状态',
        					formatter : function (value, row, index) {
        						return $.table.selectDictLabel(datas, value);
        					}
        				}, {
        					title : '操作',
        					align : 'center',
        					formatter : function (value, row, index) {
        						var actions = [];
        						actions.push('<a class="btn btn-success btn-xs" href="#"><i class="fa fa-edit"></i>编辑</a> ');
        						actions.push('<a class="btn btn-danger btn-xs" href="#"><i class="fa fa-remove"></i>删除</a>');
        						return actions.join('');
        					}
        				}
        			]
        		]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>