<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改部门')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-dept-edit" th:object="${dept}">
			<input name="deptId" type="hidden" th:field="*{deptId}" />
			<input id="treeId" name="parentId" type="hidden" th:field="*{parentId}" />
			<div class="form-group">
				<label class="col-sm-3 control-label">上级部门：</label>
				<div class="col-sm-8">
				    <div class="input-group">
						<input class="form-control" type="text" id="treeName" onclick="selectDeptTree()" readonly="true" th:field="*{parentName}">
					    <span class="input-group-addon"><i class="fa fa-search"></i></span>
				    </div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">部门名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="deptName" th:field="*{deptName}" id="deptName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">显示排序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="orderNum" th:field="*{orderNum}" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">负责人：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="leader" th:field="*{leader}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">联系电话：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="phone" th:field="*{phone}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">邮箱：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="email" th:field="*{email}">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">部门状态：</label>
				<div class="col-sm-8">
					<div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "system/dept";
		
		$("#form-dept-edit").validate({
			onkeyup: false,
			rules:{
				deptName:{
					remote: {
		                url: prefix + "/checkDeptNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"deptId": function() {
		                        return $("#deptId").val();
		                    },
		                    "parentId": function() {
		                        return $("input[name='parentId']").val();
		                    },
		        			"deptName": function() {
		                        return $.common.trim($("#deptName").val());
		                    }
		                }
		            }
				},
				orderNum:{
					digits:true
				},
				email:{
                    email:true,
        		},
        		phone:{
        			isPhone:true,
        		},
			},
			messages: {
		        "deptName": {
		            remote: "部门已经存在"
		        }
		    },
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/edit", $('#form-dept-edit').serialize());
	        }
	    }
	
		/*部门管理-修改-选择部门树*/
		function selectDeptTree() {
			var deptId = $("#treeId").val();
			var excludeId = $("input[name='deptId']").val();
			if(deptId > 0) {
			    var options = {
					title: '部门选择',
					width: "380",
					url: prefix + "/selectDeptTree/" + $("#treeId").val() + "/" + excludeId,
					callBack: doSubmit
				};
				$.modal.openOptions(options);
			} else {
        		$.modal.alertError("父部门不能选择");
        	}
		}
		
		function doSubmit(index, layero){
			var tree = layero.find("iframe")[0].contentWindow.$._tree;
			if ($.tree.notAllowLastLevel(tree)) {
	   			var body = $.modal.getChildFrame(index);
	   			$("#treeId").val(body.find('#treeId').val());
	   			$("#treeName").val(body.find('#treeName').val());
	   			$.modal.close(index);
			}
		}
	</script>
</body>
</html>
