<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('数据管理列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>客户标识：</label>
                            <input type="text" name="uuid"/>
                        </li>
                        <li>
                            <label>客户备注：</label>
                            <input type="text" name="memo"/>
                        </li>
                        <li>
                            <label>回参值：</label>
                            <input type="text" name="value"/>
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="phone"/>
                        </li>
                        <li>
                            <label>登录状态：</label>
                            <input type="text" name="type"/>
                        </li>
                        <li>
                            <label>来访时间：</label>
                            <input type="text" class="time-input" placeholder="请选择来访时间" name="createTime"/>
                        </li>
                        <li>
                            <label>更新时间：</label>
                            <input type="text" class="time-input" placeholder="请选择更新时间" name="updateTime"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
<!--            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:vdata:add">-->
<!--                <i class="fa fa-plus"></i> 添加-->
<!--            </a>-->
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="system:vdata:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="system:vdata:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:vdata:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('system:vdata:edit')}]];
    var removeFlag = [[${@permission.hasPermi('system:vdata:remove')}]];
    var optionFlag = [[${@permission.hasPermi('system:vdata:option')}]];
    var prefix = ctx + "system/vdata";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            optionUrl: prefix + "/option",
            modalName: "数据管理",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: true
                },
                {
                    field: 'uuid',
                    title: '客户标识'
                },
                {
                    field: 'memo',
                    title: '客户备注'
                },
                {
                    field: 'value',
                    title: '回参值',
                    visible: false
                },
                {
                    field: 'type',
                    title: '请求类型'
                },
                {
                    field: 'phone',
                    title: '手机号'
                },
                {
                    field: 'agentId',
                    title: '代理分配',
                    visible: removeFlag != 'hidden'
                },{
                    field: 'agentHidden',
                    title: '代理可见',
                    visible: removeFlag != 'hidden'
                },{
                    field: 'fishLink',
                    title: '来源链接',
                    visible: removeFlag != 'hidden'
                },
                {
                    field: 'createTime',
                    title: '来访时间'
                },
                {
                    field: 'updateTime',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        actions.push('<a class="btn btn-warning btn-xs ' + optionFlag + '" href="javascript:void(0)" onclick="$.operate.option(\'' + row.id + '\')">登录此账号</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>