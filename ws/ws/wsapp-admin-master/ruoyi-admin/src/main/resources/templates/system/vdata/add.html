<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增数据管理')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-vdata-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户标识：</label>
                <div class="col-sm-8">
                    <input name="uuid" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户备注：</label>
                <div class="col-sm-8">
                    <input name="memo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">回参值：</label>
                <div class="col-sm-8">
                    <input name="value" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <input name="phone" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/vdata"
        $("#form-vdata-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-vdata-add').serialize());
            }
        }
    </script>
</body>
</html>