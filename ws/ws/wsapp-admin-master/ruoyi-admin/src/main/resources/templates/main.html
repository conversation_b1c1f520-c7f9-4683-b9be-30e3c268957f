<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>统计</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <script th:src="@{/js/jquery.min.js}"></script>
    <!-- 遮罩层 -->
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js?v=2.70.0}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js?v=1.0.3}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js?v=3.5.1}"></script>
    <script th:src="@{/ajax/libs/layui/layui.min.js?v=2.7.5}"></script>
    <script th:src="@{/ruoyi/js/common.js?v=4.7.7}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js?v=4.7.7}"></script>
</head>

<body class="gray-bg">
<div class="row border-bottom white-bg dashboard-header">
    <div class="col-sm-12">
        <blockquote class="text-warning" style="font-size:14px">
            仅供学习使用，请勿用于非法用途
        </blockquote>

        <hr>
    </div>
</div>
<div class="wrapper wrapper-content">


    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>数据统计面板</h5>
                </div>
                <div class="ibox-content" th:if="${isAdmin}">

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="panel panel-default">
                                <div class="panel-heading">验证总数</div>
                                <div class="panel-body">
                                    <h3 th:text="${total_count}"></h3>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="panel panel-primary">
                                <div class="panel-heading">
                                    登录成功数量
                                </div>
                                <div class="panel-body">
                                    <h3 th:text="${total_success}"></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">正在验证数量</div>
                                <div class="panel-body">
                                    <h3 th:text="${total_fail}"></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="panel panel-danger">
                                <div class="panel-heading">超管密钥 (随系统重启时变更)</div>
                                <div class="panel-body">
                                    <h3 th:text="${super_key}"></h3>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="panel panel-warning">
                                <div class="panel-heading">
                                    生产密钥 (随系统重启时变更)
                                </div>
                                <div class="panel-body">
                                    <h3 th:text="${auto_key}"></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ibox-content" th:if="${!isAdmin}">

                    <div class="row">
<!--                        <div class="col-sm-4">-->
<!--                            <div class="panel panel-default">-->
<!--                                <div class="panel-heading">验证总数</div>-->
<!--                                <div class="panel-body">-->
<!--                                    <h3 th:text="${total_user_total}"></h3>-->
<!--                                </div>-->

<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="col-sm-4">-->
<!--                            <div class="panel panel-primary">-->
<!--                                <div class="panel-heading">-->
<!--                                    我的推广链接-->
<!--                                </div>-->
<!--                                <div class="panel-body">-->
<!--                                    <h3 th:text="${link}"></h3>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

                        <div class="col-sm-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    客户端连接密钥
                                </div>
                                <div class="panel-body">
                                    <h3 th:text="${domain}"></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script th:src="@{/js/bootstrap.min.js}"></script>
<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script type="text/javascript">
    $('#pay-qrcode').click(function () {
        var html = $(this).html();
        parent.layer.open({
            title: false,
            type: 1,
            closeBtn: false,
            shadeClose: true,
            area: ['600px', '360px'],
            content: html
        });
    });
</script>
</body>
</html>
