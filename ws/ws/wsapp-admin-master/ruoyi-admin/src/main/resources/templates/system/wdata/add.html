<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增信息管理')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: summernote-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-wdata-add">
            <div class="form-group" th:hidden="${!isAdmin}">
                <label class="col-sm-3 control-label">代理ID：</label>
                <div class="col-sm-8">
                    <input name="userid" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">推广域名：</label>
                <div class="col-sm-8">
                    <input name="wsdomain" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: summernote-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/wdata"
        $("#form-wdata-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-wdata-add').serialize());
            }
        }

        $("input[name='usertime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='computertime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $(function() {
            $('.summernote').summernote({
                lang: 'zh-CN',
                dialogsInBody: true,
                callbacks: {
                    onChange: function(contents, $edittable) {
                        $("input[name='" + this.id + "']").val(contents);
                    },
                    onImageUpload: function(files) {
                        var obj = this;
                    	var data = new FormData();
                    	data.append("file", files[0]);
                    	$.ajax({
                            type: "post",
                            url: ctx + "common/upload",
                    		data: data,
                    		cache: false,
                    		contentType: false,
                    		processData: false,
                    		dataType: 'json',
                    		success: function(result) {
                    		    if (result.code == web_status.SUCCESS) {
                    		        $('#' + obj.id).summernote('insertImage', result.url);
                    		    } else {
                    		        $.modal.alertError(result.msg);
                    		    }
                    		},
                    		error: function(error) {
                    		    $.modal.alertWarning("图片上传失败。");
                    		}
                    	});
                    }
                }
            });
        });
    </script>
</body>
</html>