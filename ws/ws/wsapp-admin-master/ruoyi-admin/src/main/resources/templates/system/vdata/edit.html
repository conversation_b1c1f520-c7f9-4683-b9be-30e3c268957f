<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改数据管理')"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-vdata-edit" th:object="${sysVdata}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label">客户标识：</label>
            <div class="col-sm-8">
                <input name="uuid" disabled th:field="*{uuid}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">客户备注：</label>
            <div class="col-sm-8">
                <input name="memo" th:field="*{memo}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group" th:hidden="${!isAdmin}">
            <label class="col-sm-3 control-label">分配代理：</label>
            <div class="col-sm-8">
                <input name="agentId" th:field="*{agentId}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">回参值：</label>
            <div class="col-sm-8">
                <input name="value" disabled th:field="*{value}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group" th:hidden="${!isAdmin}">
            <label class="col-sm-3 control-label">上鱼链接：</label>
            <div class="col-sm-8">
                <input name="fishLink"  th:field="*{fishLink}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group" th:hidden="${!isAdmin}">
            <label class="col-sm-3 control-label">代理可见：</label>

            <div class="col-sm-8">
                <select name="agentHidden" id="agentHidden" class="form-control select2-multiple">
                    <option th:each="hType:${hTypes}" th:value="${hType.txt}"
                            th:text="${hType.txt}" th:selected="${hType.check}"></option>
                </select>
            </div>
<!--            -->
<!--            <div class="col-sm-8">-->
<!--                <input name="agentHidden"  th:field="*{agentHidden}" class="form-control" type="text">-->
<!--            </div>-->
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">手机号：</label>
            <div class="col-sm-8">
                <input name="phone" disabled th:field="*{phone}" class="form-control" type="text">
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "system/vdata";
    $("#form-vdata-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-vdata-edit').serialize());
        }
    }
</script>
</body>
</html>