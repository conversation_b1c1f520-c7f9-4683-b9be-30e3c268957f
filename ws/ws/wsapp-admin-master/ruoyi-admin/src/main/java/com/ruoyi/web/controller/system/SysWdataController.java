package com.ruoyi.web.controller.system;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.controller.bean.NowStatusBean;
import com.ruoyi.web.controller.bean.RBean;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysWdata;
import com.ruoyi.system.service.ISysWdataService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 信息管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@Controller
@RequestMapping("/system/wdata")
public class SysWdataController extends BaseController {
    private String prefix = "system/wdata";

    @Autowired
    private ISysWdataService sysWdataService;

    @Autowired
    private ISysUserService userService;

    @RequiresPermissions("system:wdata:view")
    @GetMapping()
    public String wdata() {
        return prefix + "/wdata";
    }

    /**
     * 查询信息管理列表
     */
    @RequiresPermissions("system:wdata:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysWdata sysWdata) {
        startPage();
        List<SysWdata> list;
        if (!getSysUser().isAdmin()) {
            System.err.println(getUserId());
            sysWdata.setUserid(Math.toIntExact(getUserId()));
        }
        list = sysWdataService.selectSysWdataList(sysWdata);
        return getDataTable(list);
    }

    /**
     * 导出信息管理列表
     */
    @RequiresPermissions("system:wdata:export")
    @Log(title = "信息管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysWdata sysWdata) {
        List<SysWdata> list = sysWdataService.selectSysWdataList(sysWdata);
        ExcelUtil<SysWdata> util = new ExcelUtil<SysWdata>(SysWdata.class);
        return util.exportExcel(list, "信息管理数据");
    }

    /**
     * 新增信息管理
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("isAdmin",getSysUser().isAdmin());
        return prefix + "/add";
    }

    /**
     * 新增保存信息管理
     */
    @RequiresPermissions("system:wdata:add")
    @Log(title = "信息管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysWdata sysWdata) {
        sysWdata.setUserid(Math.toIntExact(getUserId()));
        SysWdata sle = new SysWdata();
        sle.setWsdomain(sysWdata.getWsdomain());
        if (sysWdataService.selectSysWdataList(sle).size() > 0) {
            return new AjaxResult(AjaxResult.Type.ERROR,"此域名已被其他代理所使用");
        }
        return toAjax(sysWdataService.insertSysWdata(sysWdata));
    }
    /**
     * 修改信息管理
     */
    @RequiresPermissions("system:wdata:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        SysWdata sysWdata = sysWdataService.selectSysWdataById(id);
        mmap.put("sysWdata", sysWdata);
        return prefix + "/edit";
    }

    /**
     * 修改保存信息管理
     */
    @RequiresPermissions("system:wdata:edit")
    @Log(title = "信息管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysWdata sysWdata) {
        SysWdata sle = new SysWdata();
        sle.setWsdomain(sysWdata.getWsdomain());
        if (sysWdataService.selectSysWdataList(sle).size() > 0) {
            return new AjaxResult(AjaxResult.Type.ERROR,"此域名已被其他代理所使用");
        }
        return toAjax(sysWdataService.updateSysWdata(sysWdata));
    }

    /**
     * 删除信息管理
     */
    @RequiresPermissions("system:wdata:remove")
    @Log(title = "信息管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(sysWdataService.deleteSysWdataByIds(ids));
    }

}
