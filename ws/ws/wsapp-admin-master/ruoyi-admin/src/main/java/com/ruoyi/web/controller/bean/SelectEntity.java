package com.ruoyi.web.controller.bean;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SelectEntity {


    private boolean check;
    private String txt;

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public String getTxt() {
        return txt;
    }

    public void setTxt(String txt) {
        this.txt = txt;
    }

    public static List<SelectEntity> getSelectType(String check,String[] types) {
        return Stream.of(types)
                .map(type -> {
                    SelectEntity entity = new SelectEntity();
                    entity.setTxt(type);
                    entity.setCheck(type.equals(check));
                    return entity;
                })
                .collect(Collectors.toList());
    }

    @Override
    public String toString() {
        return "SelectEntity{" +
                "check=" + check +
                ", txt='" + txt + '\'' +
                '}';
    }
}
