package com.ruoyi.web.controller.socket;

import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysVdataService;
import com.ruoyi.system.service.ISysWdataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;


@Configuration
public class WebSocketConfig {

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Autowired
    public void setTgService(ISysVdataService service) {
        WebSocketServer.vdataService = service;
    }

    @Autowired
    public void setUserService(ISysUserService service) {
        WebSocketServer.userService = service;
    }
    @Autowired
    public void setUserService(ISysWdataService service) {
        WebSocketServer.wdataService = service;
    }

}

