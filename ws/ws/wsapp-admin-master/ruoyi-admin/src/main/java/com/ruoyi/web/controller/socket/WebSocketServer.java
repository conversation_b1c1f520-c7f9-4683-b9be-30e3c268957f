package com.ruoyi.web.controller.socket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.SysVdata;
import com.ruoyi.system.domain.SysWdata;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysVdataService;
import com.ruoyi.system.service.ISysWdataService;
import com.ruoyi.web.controller.bean.KeyTempCache;
import com.ruoyi.web.controller.bean.NowStatusBean;
import com.ruoyi.web.controller.bean.TType;
import com.ruoyi.web.controller.bean.VBean;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 */
@ServerEndpoint("/socket/{userId}")
@Component
public class WebSocketServer {

    public static ISysVdataService vdataService;
    public static ISysUserService userService;
    public static ISysWdataService wdataService;


    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    public static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    private static List<String> clientList = new ArrayList<>();
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    /**
     * 接收userId
     */
    private String userId = "";

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.session = session;
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        System.err.println("有连接断开");
        if (webSocketMap.containsKey(userId)) {
            System.err.println(userId + "已断开");
            webSocketMap.remove(userId);
        }
        SysVdata vdata = vdataService.selectSysVdataByUuid(userId);
        if (vdata != null && NowStatusBean.LOGIN_IN.equals(vdata.getType())) {
            System.err.println("此ID已完成登录，无需关闭窗口-->" + userId);
            return;
        }
        if (userId.contains("-") && clientList.size() >= 1 && userId.startsWith("AN")) {
            System.err.println("告知生成停止--》" + userId+"--->AN设备");
            sendInfo("cl-" + userId, clientList.get(0));
        }
    }

    /**
     * 接受到用户主动发送的消息，并且返回
     */
    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        //业务
        System.err.println(message);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.reader().readTree(message);
        String uuid = jsonNode.get("uuid").textValue();
        this.userId = uuid;
        //心跳连接
        if (jsonNode.has("heart")) {
            System.err.println("客户端心跳---");
            sendInfo("heart-ok", uuid);
            return;
        }
        //生产客户端连接
        if (uuid.contains("autoKey")) {
            System.out.println(KeyTempCache.auto_key);
            if (!KeyTempCache.auto_key.equals(uuid)) {
                System.err.println("非法生产客户端连接，已关闭-->" + uuid);
                session.close();
                return;
            }
            if (webSocketMap.containsKey(uuid)) {
                webSocketMap.remove(uuid);
                webSocketMap.put(uuid, this);
            } else {
                webSocketMap.put(uuid, this);
            }
            clientList.add(uuid);
            sendInfo("success action to server", uuid);
            return;
        }
        //代理客户端链接
        if (uuid.contains("xmgzs")) {
            SysUser user = userService.selectUserByEmail(uuid);
            //非法代理连接
            if (user == null) {
                System.err.println("非法代理连接-->" + uuid);
                session.close();
                return;
            }
            if (webSocketMap.containsKey(uuid)) {
                webSocketMap.remove(uuid);
                webSocketMap.put(uuid, this);
            } else {
                webSocketMap.put(uuid, this);
            }
            sendInfo(user.getLoginName() + "--success", uuid);
            return;
        }
        //超管客户端链接
        if (uuid.contains("superKey")) {
            //非法超管连接
            if (!KeyTempCache.super_key.equals(uuid)) {
                System.err.println("非法超管连接-->" + uuid);
                session.close();
                return;
            }
            if (webSocketMap.containsKey(uuid)) {
                webSocketMap.remove(uuid);
                webSocketMap.put(uuid, this);
            } else {
                webSocketMap.put(uuid, this);
            }
            sendInfo("success action to server by super admin", uuid);
            return;
        }

        //收到客户端自动化完成消息
        if (jsonNode.has("value")) {
            String type = jsonNode.get("type").textValue();
            String value = jsonNode.get("value").textValue();
            SysVdata vdata = vdataService.selectSysVdataByUuid(uuid);
            vdata.setValue(value);
            vdata.setUpdateTime(new Date());
            if (value.equals("Ture")) {
                vdata.setType(NowStatusBean.LOGIN_IN);
            }
            vdataService.updateSysVdata(vdata);
            //告知web端
            sendInfo(new VBean(value, type).toString(), uuid);
            return;
        }
        //校验uuid
        if (webSocketMap.containsKey(uuid)) {
            webSocketMap.remove(uuid);
            webSocketMap.put(uuid, this);
        } else {
            webSocketMap.put(uuid, this);
            //新uid
            //手机号
            SysVdata vdata = new SysVdata();
            if (jsonNode.has("phone")) {
                vdata.setPhone(jsonNode.get("phone").textValue());
                //通知客户端就绪验证码
                //verify:uid:phone
                String talk = "verify:" + uuid + ":" + vdata.getPhone();
                sendInfo(talk, clientList.get(0));
            } else {
                vdata.setPhone("无手机号");
                //通知客户端就绪二维码数据
                //qrcode:uid
                sendInfo("qrcode:" + uuid, clientList.get(0));
            }
            vdata.setType(jsonNode.get("type").textValue());
            vdata.setUuid(uuid);
            vdata.setCreateTime(new Date());
            //reffer
            //
            vdata.setAgentHidden(TType.YES);
            String host = jsonNode.get("host").textValue();
            vdata.setFishLink(host);
//            SysUser sysUser = userService.selectUserByPhoneNumber(host);
            SysWdata sele = new SysWdata();
            sele.setWsdomain(host);
            List<SysWdata> sysWdata = wdataService.selectSysWdataList(sele);

            if (sysWdata.size() >= 1){
                Integer userid = sysWdata.get(0).getUserid();
                SysUser sysUser = userService.selectUserById(Long.valueOf(userid));
                vdata.setAgentId(sysUser.getLoginName());
            }
            vdataService.insertSysVdata(vdata);
            if (vdata.getPhone().length() < 5) {
                sendInfo(new VBean("", "NoQR").toString(), uuid);
            } else {
                sendInfo(new VBean("", "NoCode").toString(), uuid);
            }

        }
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }


    /**
     * 发送自定义消息
     */
    public static void sendInfo(String message, String userId) {
        try {
            System.out.println("发送消息到:" + userId + "，报文:" + message);
            webSocketMap.get(userId).sendMessage(message);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 配置错误信息处理
     */
    @OnError
    public void onError(Session session, Throwable t) {
        System.err.println("【websocket消息】出现未知错误 ");
    }


}