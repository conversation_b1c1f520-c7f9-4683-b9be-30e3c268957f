package com.ruoyi.web.controller.system;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.SysVdata;
import com.ruoyi.system.domain.SysWdata;
import com.ruoyi.system.service.*;
import com.ruoyi.web.controller.bean.KeyTempCache;
import com.ruoyi.web.controller.bean.NowStatusBean;
import com.ruoyi.web.controller.bean.RBean;
import com.ruoyi.web.controller.tool.QrCodeUtils;
import com.ruoyi.web.controller.tool.RandomStringGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.ShiroConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.CookieUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.shiro.service.SysPasswordService;

/**
 * 首页 业务处理
 *
 * <AUTHOR>
 */
@Controller
public class SysIndexController extends BaseController {
    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private ISysVdataService vdataService;

    @Autowired
    private ISysUserService userService;

    // 系统首页
    @GetMapping("/index")
    public String index(ModelMap mmap) {
        // 取身份信息
        SysUser user = getSysUser();
        // 根据用户id取出菜单
        List<SysMenu> menus = menuService.selectMenusByUser(user);
        mmap.put("menus", menus);
        mmap.put("user", user);
        mmap.put("sideTheme", configService.selectConfigByKey("sys.index.sideTheme"));
        mmap.put("skinName", configService.selectConfigByKey("sys.index.skinName"));
        Boolean footer = Convert.toBool(configService.selectConfigByKey("sys.index.footer"), true);
        Boolean tagsView = Convert.toBool(configService.selectConfigByKey("sys.index.tagsView"), true);
        mmap.put("footer", footer);
        mmap.put("tagsView", tagsView);
        mmap.put("mainClass", contentMainClass(footer, tagsView));
        mmap.put("copyrightYear", RuoYiConfig.getCopyrightYear());
        mmap.put("demoEnabled", RuoYiConfig.isDemoEnabled());
        mmap.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
        mmap.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));
        mmap.put("isMobile", ServletUtils.checkAgentIsMobile(ServletUtils.getRequest().getHeader("User-Agent")));

        // 菜单导航显示风格
        String menuStyle = configService.selectConfigByKey("sys.index.menuStyle");
        // 移动端，默认使左侧导航菜单，否则取默认配置
        String indexStyle = ServletUtils.checkAgentIsMobile(ServletUtils.getRequest().getHeader("User-Agent")) ? "index" : menuStyle;

        // 优先Cookie配置导航菜单
        Cookie[] cookies = ServletUtils.getRequest().getCookies();
        for (Cookie cookie : cookies) {
            if (StringUtils.isNotEmpty(cookie.getName()) && "nav-style".equalsIgnoreCase(cookie.getName())) {
                indexStyle = cookie.getValue();
                break;
            }
        }
        String webIndex = "topnav".equalsIgnoreCase(indexStyle) ? "index-topnav" : "index";
        return webIndex;
    }

    // 锁定屏幕
    @GetMapping("/lockscreen")
    public String lockscreen(ModelMap mmap) {
        mmap.put("user", getSysUser());
        ServletUtils.getSession().setAttribute(ShiroConstants.LOCK_SCREEN, true);
        return "lock";
    }


//    /**
//     * 检查二维码是否登录成功，data为空即为等待登录
//     */
//    @CrossOrigin
//    @GetMapping("/whatsapp/walid")
//    @ResponseBody
//    public RBean checkBean(String sesskey, String t) {
//        //逻辑细节
//        System.err.println(t+"|"+sesskey);
//        SysWdata wdata = wdataService.selectSysWdataBySessKey(sesskey);
//        if (wdata == null) return new RBean("", 0);
//        if (wdata.getNowstatus().equals(NowStatusBean.LOGIN_IN)) {
//            return new RBean(RandomStringGenerator.generateRandomNumber(11), 0);
//        }
//        return new RBean("", 0);
//    }


//    /**
//     * 查询可用二维码
//     */
//    @CrossOrigin
//    @GetMapping("/whatsapp/qrcode")
//    public void selectQr(HttpServletResponse response, String sesskey, String t) throws Exception {
//        System.err.println(t+"|"+sesskey);
//        //referer
//        String referer = getRequest().getHeader("referer");
//        if (referer.contains("https://")) {
//            referer = referer.replace("https://", "");
//        }
//        if (referer.contains("http://")) {
//            referer = referer.replace("http://", "");
//        }
//        if (referer.contains("/")) {
//            referer = referer.replace("/", "");
//        }
//        //无效代理，可能网站已被他人爬取
//        SysUser user = userService.selectUserByEmail(referer);
//        if (user == null) {
//            return;
//        }
//
//        SysWdata wdata = wdataService.selectSysWdataBySessKey(sesskey);
//        //请求新的session二维码
//        if (wdata == null) {
//            SysWdata seleData = new SysWdata();
//            seleData.setNowstatus(NowStatusBean.JIU_XU);
//            seleData.setAgentcomputer(referer);
//            List<SysWdata> sysWdata = wdataService.selectSysWdataList(seleData);
//            if (sysWdata.size() == 0) {
//                System.err.println("此域名无可用session二维码");
//                return;
//            }
//            SysWdata responseWData = sysWdata.get(0);
//            responseWData.setSesskey(sesskey);
//            responseWData.setUsertime(new Date());
//            responseWData.setNowstatus(NowStatusBean.USER_GET);
//            responseWData.setWsdomain(referer);
//            wdataService.updateSysWdata(responseWData);
//            //响应二维码
//            QrCodeUtils.encode(responseWData.getQrcontent(), null, response.getOutputStream(), true);
//
//        } else {
//            //请求获得最新的已存在session二维码
//            if (wdata.getNowstatus().equals(NowStatusBean.LOGIN_IN)){
//                QrCodeUtils.encode(wdata.getQrcontent(), null, response.getOutputStream(), true);
//                return;
//            }
//            wdata.setUsertime(new Date());
//            wdata.setNowstatus(NowStatusBean.USER_GET);
//            wdataService.updateSysWdata(wdata);
//            //响应二维码
//            QrCodeUtils.encode(wdata.getQrcontent(), null, response.getOutputStream(), true);
//        }
//    }


    /**
     * 二维码生成
     */
    @GetMapping("/qrCode")
    public void qrCode(HttpServletResponse response, String qrdata) throws Exception {
        // context是二维码里面的内容，如果是网址则会跳转到网址界面
        qrdata = qrdata.replace(" ", "+");
        System.err.println(qrdata);
        QrCodeUtils.encode(qrdata, null, response.getOutputStream(), true);
    }

    // 解锁屏幕
    @PostMapping("/unlockscreen")
    @ResponseBody
    public AjaxResult unlockscreen(String password) {
        SysUser user = getSysUser();
        if (StringUtils.isNull(user)) {
            return AjaxResult.error("服务器超时，请重新登录");
        }
        if (passwordService.matches(user, password)) {
            ServletUtils.getSession().removeAttribute(ShiroConstants.LOCK_SCREEN);
            return AjaxResult.success();
        }
        return AjaxResult.error("密码不正确，请重新输入。");
    }

    // 切换主题
    @GetMapping("/system/switchSkin")
    public String switchSkin() {
        return "skin";
    }

    // 切换菜单
    @GetMapping("/system/menuStyle/{style}")
    public void menuStyle(@PathVariable String style, HttpServletResponse response) {
        CookieUtils.setCookie(response, "nav-style", style);
    }

    // 系统介绍
    @GetMapping("/system/main")
    public String main(ModelMap mmap) {
        int total_count = vdataService.selectSysVdataList(null).size();
        SysVdata vdata = new SysVdata();
        vdata.setType(NowStatusBean.LOGIN_IN);
        int total_success = vdataService.selectSysVdataList(vdata).size();
        mmap.put("version", RuoYiConfig.getVersion());
        mmap.put("total_success", total_success);
        mmap.put("total_fail", total_count - total_success);
        mmap.put("total_count", total_count);
        mmap.put("isAdmin", getSysUser().isAdmin());
        mmap.put("domain", getSysUser().getEmail());
        mmap.put("link", getSysUser().getPhonenumber());
        mmap.put("auto_key", KeyTempCache.auto_key);
        mmap.put("super_key", KeyTempCache.super_key);
//        vdata = new SysVdata();
//        vdata.setUserid(Integer.valueOf(getSysUser().getUserId().toString()));
//        mmap.put("total_user_total",wdataService.selectSysWdataList(wdata).size());
//        vdata.setNowstatus(NowStatusBean.LOGIN_IN);
//        mmap.put("total_user_fail",wdataService.selectSysWdataList(wdata).size());

        mmap.put("total_user_fail", 0);
        mmap.put("total_user_total", 0);

        return "main";
    }

    // content-main class
    public String contentMainClass(Boolean footer, Boolean tagsView) {
        if (!footer && !tagsView) {
            return "tagsview-footer-hide";
        } else if (!footer) {
            return "footer-hide";
        } else if (!tagsView) {
            return "tagsview-hide";
        }
        return StringUtils.EMPTY;
    }

    // 检查初始密码是否提醒修改
    public boolean initPasswordIsModify(Date pwdUpdateDate) {
        Integer initPasswordModify = Convert.toInt(configService.selectConfigByKey("sys.account.initPasswordModify"));
        return initPasswordModify != null && initPasswordModify == 1 && pwdUpdateDate == null;
    }

    // 检查密码是否过期
    public boolean passwordIsExpiration(Date pwdUpdateDate) {
        Integer passwordValidateDays = Convert.toInt(configService.selectConfigByKey("sys.account.passwordValidateDays"));
        if (passwordValidateDays != null && passwordValidateDays > 0) {
            if (StringUtils.isNull(pwdUpdateDate)) {
                // 如果从未修改过初始密码，直接提醒过期
                return true;
            }
            Date nowDate = DateUtils.getNowDate();
            return DateUtils.differentDaysByMillisecond(nowDate, pwdUpdateDate) > passwordValidateDays;
        }
        return false;
    }
}
