package com.ruoyi.web.controller.system;

import java.io.*;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.json.JSONObject;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.web.controller.bean.KeyTempCache;
import com.ruoyi.web.controller.bean.NowStatusBean;
import com.ruoyi.web.controller.bean.SelectEntity;
import com.ruoyi.web.controller.bean.TType;
import com.ruoyi.web.controller.socket.WebSocketServer;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysVdata;
import com.ruoyi.system.service.ISysVdataService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

import static com.ruoyi.web.controller.socket.WebSocketServer.vdataService;

/**
 * 数据管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Controller
@RequestMapping("/system/vdata")
public class SysVdataController extends BaseController {
    private String prefix = "system/vdata";

    @Autowired
    private ISysVdataService sysVdataService;

    @RequiresPermissions("system:vdata:view")
    @GetMapping()
    public String vdata() {
        return prefix + "/vdata";
    }

    /**
     * 查询数据管理列表
     */
    @SuppressWarnings("rawtypes")
    @RequiresPermissions("system:vdata:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysVdata sysVdata) {
        if (!getSysUser().isAdmin()) {
            sysVdata.setAgentId(getSysUser().getUserName());
        }
        startPage();
        long offset = 0;
        List<SysVdata> list = sysVdataService.selectSysVdataList(sysVdata);
        if (!getSysUser().isAdmin()) {
            String HiddenStr = "不可见";
            Iterator<SysVdata> iterator = list.iterator();
            while (iterator.hasNext()) {
                SysVdata sysVdata1 = iterator.next();
                if (sysVdata1.getAgentHidden().equals(HiddenStr)) {
                    iterator.remove();
                    offset++;
                }
            }
        }
        // if (!getSysUser().isAdmin()) {
        //     list = list.stream().filter(data -> TType.YES.equals(data.getAgentHidden())).collect(Collectors.toList());
        // }
        return getDataTable(list, -offset);
    }

    /**
     * 导出数据管理列表
     */
    @RequiresPermissions("system:vdata:export")
    @Log(title = "数据管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysVdata sysVdata) {
        List<SysVdata> list = sysVdataService.selectSysVdataList(sysVdata);
        ExcelUtil<SysVdata> util = new ExcelUtil<SysVdata>(SysVdata.class);
        return util.exportExcel(list, "数据管理数据");
    }

    /**
     * 新增数据管理
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存数据管理
     */
    @RequiresPermissions("system:vdata:add")
    @Log(title = "数据管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysVdata sysVdata) {
        return toAjax(sysVdataService.insertSysVdata(sysVdata));
    }

    /**
     * 修改数据管理
     */
    @RequiresPermissions("system:vdata:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SysVdata sysVdata = sysVdataService.selectSysVdataById(id);
        mmap.put("sysVdata", sysVdata);
        mmap.put("isAdmin", getSysUser().isAdmin());
        mmap.put("hTypes", SelectEntity.getSelectType(sysVdata.getAgentHidden(), TType.types));
        return prefix + "/edit";
    }

    /**
     * 修改保存数据管理
     */
    @RequiresPermissions("system:vdata:edit")
    @Log(title = "数据管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysVdata sysVdata) {
        return toAjax(sysVdataService.updateSysVdata(sysVdata));
    }

    /**
     * 删除数据管理
     */
    @RequiresPermissions("system:vdata:remove")
    @Log(title = "数据管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(sysVdataService.deleteSysVdataByIds(ids));
    }

    /**
     * 登录数据管理
     */
    @RequiresPermissions("system:vdata:option")
    @PostMapping("/option")
    @ResponseBody
    public AjaxResult option(Long id) {
        SysVdata vdata = sysVdataService.selectSysVdataById(id);
        //鉴权
        ConcurrentHashMap<String, WebSocketServer> webSocketMap = WebSocketServer.webSocketMap;
        if (!webSocketMap.containsKey(getSysUser().getEmail()) && !getSysUser().isAdmin()) {
            return new AjaxResult(AjaxResult.Type.ERROR, "登陆失败！您的客户端不在线");
        }
        //超管
        if (getSysUser().isAdmin() && !webSocketMap.containsKey(KeyTempCache.super_key)) {
            return new AjaxResult(AjaxResult.Type.ERROR, "登陆失败！您的超管客户端不在线");
        }
        //识别
        if (!NowStatusBean.LOGIN_IN.equals(vdata.getType())) {
            return new AjaxResult(AjaxResult.Type.ERROR, "登录失败。这是一个未完成验证的账户");
        }
        //通知
        try {
            webSocketMap.get(getSysUser().isAdmin() ? KeyTempCache.super_key : getSysUser().getEmail()).sendMessage("st-" + vdata.getUuid());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return new AjaxResult(AjaxResult.Type.SUCCESS, "操作登录成功，请移步至本地客户端查看登录进度");
    }

    @RequestMapping("/upload")
    @ResponseBody
    public AjaxResult httpUpload(@RequestParam("file") MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            String uuid = fileName.replace(".zip", "");
            SysVdata vdata = vdataService.selectSysVdataByUuid(uuid);
            vdata.setType(NowStatusBean.LOGIN_IN);
            vdataService.updateSysVdata(vdata);
            FileUploadUtils.upload(file);
            return new AjaxResult(AjaxResult.Type.SUCCESS, "success");
        } catch (IOException e) {
            e.printStackTrace();
            return new AjaxResult(AjaxResult.Type.ERROR, "error");
        }
    }

    @RequestMapping("/download")
    @ResponseBody
    public String fileDownLoad(HttpServletResponse response, @RequestParam("fileName") String fileName) {
        File file = new File("upData" + '/' + fileName);
        if (!file.exists()) {
            file = new File(fileName);
        }
         if (!file.exists()) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return file.getAbsolutePath() + "下载文件不存在";
        }
        response.reset();
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        response.setContentLength((int) file.length());
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));) {
            byte[] buff = new byte[1024];
            OutputStream os = response.getOutputStream();
            int i = 0;
            while ((i = bis.read(buff)) != -1) {
                os.write(buff, 0, i);
                os.flush();
            }
        } catch (IOException e) {
            return "下载失败";
        }
        return "下载成功";
    }
}
