{"version": "0.2.0", "configurations": [{"name": "启动", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run-script", "start"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "child/whatsappone.js", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "electron", "runtimeArgs": ["child/whatsappone.js"], "args": ["--id=1"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}