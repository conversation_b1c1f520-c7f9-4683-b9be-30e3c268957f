const global_module = require('./module/global_module');
const axios = require('axios');
const path = require('path');
const { spawn, exec, fork } = require('child_process');
const fs = require('fs');
const Path = require('path');
const MySql = require('mysql');

var globalVariables = new Map();
const affiliated = { axios, globalVariables, global_module };

const allowMethod = new Map();
allowMethod.set('POST', 1);
allowMethod.set('GET', 1);

function initMySql() {
    return new Promise((resolve) => {
        let pool = MySql.createPool({
            connectionLimit: 10,
            host: '127.0.0.1',
            user: '!t*M5k@Vx#x*!^#@',
            password: '!t*M5k@Vx#x*!^#@',
            database: '*%F$&7@@e*^pHp!r'
        });
        let obj = {
            pool: pool,
            run: (sql) => {
                return new Promise((resolve) => {
                    pool.query(sql, function (error, results, fields) {
                        if (error) {
                            setTimeout(async () => {
                                obj = await initMySql();
                                resolve(await obj.run(sql));
                            }, 2000);
                        }
                        resolve(results);
                    });
                });
            },
            close: () => {
                pool.end();
            }
        };
        resolve(obj);
    });
}

(async () => {
    // let Sql = await initMySql();
    let Sql = null;
    var globalVariable = new Map();
    globalVariable.set('fileData', new Map());
    const express = require('express');
    const app = express();
    app.use('/', express.static('h5'));
    // app.use('/admin', express.static('admin'));
    app.use(express.urlencoded({ extended: false }));
    app.use(express.json({ limit: '10mb', extended: true }));
    const RequireCache = new Map();
    let port = 7001;
    app.listen(port, () => {
        console.log('URL: http://127.0.0.1:' + port);
    });
    let isDebug = process.debugPort !== undefined;
    let apiPath = '/api/*';
    app.all(apiPath, (req, res) => {
        let host = req.headers.host;
        if (host !== '127.0.0.1:' + port && host !== 'localhost:' + port) {
            res.status(404);
            return;
        }
        res.set({
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type'
        });
        if (!allowMethod.get(req.method)) {
            res.status(200).send('');
            return;
        }
        let url = req.url;
        let urlObj = new URL(url, 'http://' + host);
        let pathname = urlObj.pathname;
        if (apiPath && apiPath != '' && apiPath != '*') {
            let newApiPath = apiPath.replace('*', '');
            pathname = pathname.replace(newApiPath, '/');
        }
        if (pathname === '/') {
            res.send('');
            return;
        }
        let isDirPath = true;
        if (pathname.lastIndexOf('.') > pathname.lastIndexOf('/')) {
            isDirPath = false;
        }
        if (!isDirPath) {
            res.send('');
            return;
        }
        let lastIndexValue = pathname.charAt(pathname.length - 1);
        if (lastIndexValue !== '/') {
            pathname = pathname + '/';
        }
        let Require = null;
        let funName = '';
        let parentDir = '';
        let InOp = { req, res, urlObj, affiliated, exec, isDebug, Sql };
        try {
            parentDir = pathname.substring(0, pathname.lastIndexOf('/', pathname.length - 2) + 1);
            funName = pathname.substring(parentDir.length, pathname.length - 1);
            let Key = '.' + parentDir + 'index.js';
            let cacheRequire = RequireCache.get(Key);
            if (cacheRequire) {
                Require = cacheRequire;
            } else {
                Require = require(Key);
                try {
                    Require.init();
                } catch (e) {}
                RequireCache.set(Key, Require);
            }
        } catch (e) {}
        if (!Require || parentDir == '') {
            res.send('');
            return;
        }
        if (!funName || funName === '') {
            res.send('');
            return;
        }
        (async (op = InOp) => {
            let ret = null;
            try {
                let fun = Require[funName];
                if (!fun || typeof fun !== 'function') {
                    // retJson = { state: 'error', msg: '路径存在,但没有指定的操作函数' };
                    // setResJsonType(res);
                    res.send('');
                    return;
                }
                ret = await fun(op);
            } catch (e) {}
            if (!ret) {
                res.send('');
                return;
            }
            res.send(ret);
        })();
    });
    function initWhatsAppOne() {
        let Key = './whatsappone/index/index.js';
        let Require = require(Key);
        try {
            Require.init();
        } catch (e) {}
        RequireCache.set(Key, Require);
    }
    initWhatsAppOne();
})();
