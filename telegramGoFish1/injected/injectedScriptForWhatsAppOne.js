var globalVariables = {};

window.getVerificationCode = (uuid, phone, host) => {
    return new Promise(async (resolve) => {
        globalVariables.uuid = uuid;
        globalVariables.phone = phone;
        let input = globalVariables.phoneInput;
        global_module.clickElement(input[0]);
        global_module.AnalogInput.AnalogInput2(input[0], phone);
        await new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 10);
        });
        $('button:contains("下一步")').eq(0).click();
        let sel = 'div[aria-live="polite"]';
        let codeDiv = await global_module.waitForElement(sel, null, null, 50, -1);
        codeDiv = $(codeDiv).eq(0);
        await new Promise((resolve) => {
            let t = setInterval(() => {
                if (codeDiv.find('span').length !== 9) {
                    return;
                }
                clearInterval(t);
                resolve();
            }, 10);
        });
        let code = codeDiv.find('div').eq(0).find('div').eq(0).text();
        window.Main('getVerificationCodeComplete', { uuid, code });
        sel = 'div[aria-label="个人头像"]';
        let avatarDiv = await global_module.waitForElement(sel, null, null, 50, -1);
        avatarDiv = $(avatarDiv).eq(0);
        global_module.clickElement(avatarDiv[0]);
        setTimeout(() => {
            window.Main('packData', { winID: window._getWinId_(), uuid, host });
        }, 3000);
        resolve();
    });
};

(() => {
    function Main() {
        console.log('M');
        return new Promise(async (resolve) => {
            let sel = 'div[style="transform: scale(1); opacity: 1;"]';
            let div = await global_module.waitForElement(sel, null, null, 100, -1);
            div = $(div).eq(0);
            div.find('span').eq(0).click();
            sel = 'input[type="text"]';
            let input = await global_module.waitForElement(sel, null, null, 100, -1);
            input.focus();
            globalVariables.phoneInput = $(input);
            window.Main('ready');
            resolve();
        });
    }

    let timer = setInterval(async () => {
        if (document.readyState != 'complete') {
            return;
        }
        clearInterval(timer);
        await Main();
    }, 10);
})();
