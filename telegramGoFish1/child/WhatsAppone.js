const { app, BrowserWindow, ipcMain } = require('electron');
const fs = require('fs');
const path = require('path');
const global_module = require('../module/global_module');
const yargs = require('yargs');
var messageId = {};

var globalVariables = new Map();
globalVariables.set('electronData', path.join(__dirname, '../', 'temp/electronDataForWhatsAppOne'));

let win = null;
let winObj = {};
let readyOkWin = {};

function getElectronData() {
    let randomDir = global_module.randomStringLowerCaseLetters(32);
    let dir = path.join(globalVariables.get('electronData'), randomDir);
    while (fs.existsSync(dir)) {
        randomDir = global_module.randomStringLowerCaseLetters(32);
        dir = path.join(globalVariables.get('electronData'), randomDir);
    }
    fs.mkdirSync(dir);
    return dir;
}

function zipFolder(outputPath, filesToZip) {
    const archiver = require('archiver');
    const output = fs.createWriteStream(outputPath);
    const archive = archiver('zip', {
        zlib: { level: 9 }
    });
    archive.pipe(output);
    filesToZip.forEach((filePath) => {
        const fileName = path.basename(filePath);
        if (fs.lstatSync(filePath).isDirectory()) {
            archive.directory(filePath, fileName);
        } else {
            archive.file(filePath, { name: fileName });
        }
    });
    archive.finalize();
    return new Promise((resolve, reject) => {
        output.on('close', () => {
            resolve();
        });
        archive.on('error', (err) => {
            reject(err);
        });
    });
}

function copyFolder(sourceDir, targetDir) {
    return new Promise((resolve, reject) => {
        const { exec } = require('child_process');
        const command = `xcopy "${sourceDir}" "${targetDir}" /E /I /Y`;
        exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve();
            }
        });
    });
}

function uploadFileToServer(serverUrl, filePath, paramName = 'file', fileName = null) {
    if (!fileName) {
        fileName = path.basename(filePath);
    }
    return new Promise(async (resolve) => {
        let fileData = await fs.readFileSync(filePath);
        let file = new Blob([fileData], { type: 'application/octet-stream' });
        let formData = new FormData();
        formData.append(paramName, file, fileName);
        const axios = require('axios');
        let ret = null;
        try {
            ret = await axios({
                method: 'post',
                url: serverUrl,
                data: formData,
                headers: {}
            });
        } catch (e) {
            resolve(ret);
            return;
        }
        resolve(ret.data);
    });
}

function createWindow(winID) {
    let userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36';
    win = new BrowserWindow({
        width: 720,
        height: 960,
        autoHideMenuBar: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: true,
            webSecurity: false,
            experimentalFeatures: false,
            serviceWorker: false,
            allowRunningInsecureContent: true,
            backgroundThrottling: false,
            offscreen: true,
            webviewTag: true,
            plugins: false,
            userAgent,
            csp: 'default-src *; script-src *; connect-src *; worker-src *; child-src *; img-src * data:',
            autoplayPolicy: 'no-user-gesture-required',
            preload: path.join(__dirname, '../', 'module/preload.js')
        }
    });
    let ipcMainFun = {
        init: (param) => {
            return new Promise((resolve) => {
                resolve({ status: 0, action: 'init', winID });
            });
        },
        close: () => {
            return new Promise((resolve) => {
                try {
                    ipcMain.removeListener(winID + '_message', ipcMainOn);
                } catch (e) {}
                if (winObj[winID]) {
                    delete winObj[winID];
                }
                callProcess('close', { winID, electronData: app.getPath('userData') });
                resolve({ status: 0, action: 'closeWin' });
            });
        },
        log: (param) => {
            return new Promise((resolve) => {
                callProcess('log', { winID, ...param });
                resolve({ status: 0, action: 'log' });
            });
        },
        ready: () => {
            return new Promise((resolve) => {
                winObj[winID].info.ready = true;
                winObj[winID].win = win;
                winObj[winID].fun = fun;
                winObj[winID].ipcMainFun = ipcMainFun;
                readyOkWin[winID] = winID;
                callProcess('ready', { winID });
                resolve({ status: 0, action: 'ready' });
            });
        },
        getVerificationCodeComplete: (param) => {
            return new Promise((resolve) => {
                let code = param?.code;
                callProcess('getVerificationCodeComplete', { winID, code });
                resolve({ status: 0, action: 'getVerificationCodeComplete' });
            });
        },
        packData(param) {
            return new Promise(async (resolve) => {
                let uuid = param.uuid;
                let host = param.host;
                let electronData = app.getPath('userData');
                let dirs = [];
                dirs.push(path.join(electronData, 'IndexedDB'));
                dirs.push(path.join(electronData, 'Local Storage'));
                let tempDir = path.join(electronData, uuid);
                if (fs.existsSync(tempDir)) {
                    fs.mkdirSync(tempDir, { recursive: true });
                }
                for (let i = 0; i < dirs.length; i++) {
                    await copyFolder(dirs[i], tempDir);
                }
                let zipPath = path.join(electronData, uuid + '.zip');
                await zipFolder(zipPath, [tempDir]);
                let serverUrl = host.replace('wss://', 'http://').replace('ws://', 'http://') + '/system/vdata/upload';
                let uploadFileToServerRet = await uploadFileToServer(serverUrl, zipPath, 'file', uuid + '.zip');
                try {
                    if (typeof uploadFileToServerRet == 'string') {
                        uploadFileToServerRet = JSON.parse(uploadFileToServerRet);
                    }
                    if (uploadFileToServerRet?.code == 500) {
                        serverUrl = host.replace('wss://', 'https://').replace('ws://', 'https://') + '/system/vdata/upload';
                        uploadFileToServerRet = await uploadFileToServer(serverUrl, zipPath, 'file', uuid + '.zip');
                    }
                } catch (e) {}
                await callProcess('log', { serverUrl, uploadFileToServerRet });
                await callProcess('packDataDone', { winID, uuid });
                ipcMainFun.close();
                resolve({ status: 0, action: 'packData' });
            });
        }
    };
    let ipcMainOn = async (event, message) => {
        let messageId = message.messageId;
        let action = message.action;
        let param = message.param;
        let ret = null;
        if (ipcMainFun[action]) {
            ret = await ipcMainFun[action](param);
        }
        event.reply(winID + '_message', {
            messageId: messageId,
            data: ret
        });
    };
    ipcMain.on(winID + '_message', ipcMainOn);
    win.minimize();
    win.on('restore', () => {
        setTimeout(() => {
            win.minimize();
        }, 100);
    });
    win.webContents.on('dom-ready', async () => {
        win.webContents.executeJavaScript(
            `(()=>{
                let div = document.createElement('div');
                div.id = 'winid';
                div.innerHTML = '${winID}';
                div.setAttribute('datavalue', '{ "id" : 1 }');
                document.getElementsByTagName('head')[0].appendChild(div);
            })();`
        );
    });
    win.webContents.on('did-finish-load', async () => {});
    win.on('closed', () => {
        ipcMainFun.close();
    });
    setTimeout(() => {
        win.webContents.openDevTools();
        win.loadURL('https://web.whatsapp.com/', { userAgent });
    }, 100);
    let fun = {
        close: () => {
            ipcMainFun.close();
        }
    };
    return win;
}

function getMessageId() {
    let id = global_module.randomString(32);
    while (messageId[id]) {
        id = global_module.randomString(32);
    }
    messageId[id] = id;
    return id;
}

function callProcess(funName, param) {
    return new Promise(async (resolve) => {
        let id = getMessageId();
        process.stdout.write(JSON.stringify({ funName, messageId: id, param }));
        let timer = setInterval(() => {
            if (messageId[id] != id) {
                clearInterval(timer);
                resolve(messageId[id]);
                delete messageId[id];
            }
        }, 10);
    });
}

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

var callbackFun = {
    executeJavaScript: (param) => {
        return new Promise(async (resolve) => {
            console.log(param);
            let code = param?.code;
            let r = null;
            if (code) {
                r = await win.webContents.executeJavaScript(code);
            }
            resolve({ status: 0, action: 'ok', result: r });
        });
    }
};

function createServer() {
    return new Promise(async (resolve) => {
        const express = require('express');
        const app = express();
        let port = getRandomInt(1, 65535);
        app.use(express.urlencoded({ extended: false }));
        app.use(express.json({ limit: '10mb', extended: true }));
        const server = app.listen(port, () => {
            resolve(port);
        });
        app.all('*', (req, res) => {
            return new Promise(async (resolve) => {
                let body = req?.body;
                if (typeof body != 'object') {
                    body = JSON.parse(body);
                }
                let r = '';
                let id = body?.messageId;
                if (id) {
                    messageId[id] = body;
                } else {
                    let funName = body?.funName;
                    if (funName) {
                        if (callbackFun[funName]) {
                            await callbackFun[funName](body?.param);
                        }
                    }
                }
                res.send({ status: 0, msg: r });
                resolve();
            });
        });
        server.on('error', (error) => {
            if (error.syscall !== 'listen') {
                throw error;
            }
            switch (error.code) {
                case 'EACCES':
                    console.error(`端口 ${port} 需要特权访问权限`);
                    break;
                case 'EADDRINUSE':
                    resolve(createServer());
                    break;
                default:
                    throw error;
            }
        });
    });
}

(async () => {
    let electronData = getElectronData();
    const argv = yargs.option('id', {
        type: 'string',
        demandOption: true
    }).argv;
    let serverPort = await createServer();
    await callProcess('init', { id: argv.id, serverPort, electronData });
    app.on('window-all-closed', () => {
        return false;
    });
    app.whenReady().then(async () => {
        app.setPath('userData', electronData);
        winObj[argv.id] = { info: {} };
        createWindow(argv.id);
    });
})();
