const fs = require('fs');

class global_module {
    saveBase64ImageToFile(base64Data, filePath) {
        const base64Image = base64Data.replace(/^data:image\/\w+;base64,/, '');
        const writeStream = fs.createWriteStream(filePath, { flags: 'w', encoding: 'base64' });
        writeStream.write(base64Image, 'base64');
        writeStream.end();
    }
    
    GetUrlParm(href = null, name) {
        if (href == null) {
            href = location.href;
        }
        let parms = href.substring(href.indexOf('?') + 1);
        let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        let r = parms.match(reg);
        if (r != null) {
            return decodeURIComponent(r[2]);
        }
        return null;
    }

    SetUrlParm(href, name, value) {
        if (href == null || href == '') {
            href = location.href;
        }
        let index = href.indexOf('?');
        if (index == -1) {
            href += '?' + name + '=' + value;
        } else {
            let parms = href.substring(href.indexOf('?') + 1);
            let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
            let r = parms.match(reg);
            if (r != null) {
                href = href.replace(r[0], r[0].replace(r[2], value));
            } else {
                href += '&' + name + '=' + value;
            }
        }
        return href;
    }

    randomString(len) {
        len = len || 32;
        let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        let maxPos = chars.length;
        let pwd = '';
        for (let i = 0; i < len; i++) {
            pwd += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return pwd;
    }

    randomStringLowerCaseLetters(len) {
        len = len || 32;
        let chars = 'abcdefhijkmnprstwxyz';
        let maxPos = chars.length;
        let pwd = '';
        for (let i = 0; i < len; i++) {
            pwd += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return pwd;
    }

    getMidStr(text, startStr, endStr, index = 0) {
        let start = text.indexOf(startStr, index);
        if (start === -1) {
            return null;
        }
        start += startStr.length;
        let end = text.indexOf(endStr, start);
        let midText = text.substring(start, end);
        return { start, end, midText };
    }

    getMidStrList(text, startStr, endStr) {
        let list = [];
        let start = 0;
        while (true) {
            let temp = this.getMidStr(text, startStr, endStr, start);
            if (!temp) {
                break;
            }
            list.push(temp);
            start = temp.end;
        }
        return list;
    }

    RandomIP() {
        let ip = '';
        for (let i = 0; i < 4; i++) {
            ip += Math.floor(Math.random() * 255) + '.';
        }
        ip = ip.substring(0, ip.length - 1);
        return ip;
    }

    RandomIPHeader() {
        let ip = this.RandomIP();
        let header = {
            'X-Forwarded-For': ip,
            'X-Originating-IP': ip,
            'X-Remote-Addr': ip,
            'X-Remote-IP': ip
        };
        return header;
    }

    jsonToUrl(postData) {
        let jsonString = '';
        for (let key in postData) {
            jsonString += `${key}=${postData[key]}&`;
        }
        if (jsonString.charAt(jsonString.length - 1) === '&') {
            jsonString = jsonString.substr(0, jsonString.length - 1);
        }
        return jsonString;
    }
}

module.exports = new global_module();
