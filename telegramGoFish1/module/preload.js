const { ipc<PERSON><PERSON><PERSON>, contextBridge } = require('electron');
const fs = require('fs');
const path = require('path');
const global_module = require('../module/global_module');
const axios = require('axios');

var __winId__ = '';
var datavalue = {};
var globalVariables = new Map();
var messageId = {};

function getMessageId() {
    let id = global_module.randomString(32);
    while (messageId[id]) {
        id = global_module.randomString(32);
    }
    messageId[id] = id;
    return id;
}

function sendMain(action, param) {
    return new Promise(async (resolve) => {
        let id = getMessageId();
        ipcRenderer.send(__winId__ + '_message', { messageId: id, action, param });
        let timer = setInterval(() => {
            if (messageId[id] != id) {
                clearInterval(timer);
                resolve(messageId[id]);
                delete messageId[id];
            }
        }, 10);
    });
}

function injectedScript() {
    let injecteds = [];
    injecteds.push(path.join(__dirname, '../injected/jquery.js'));
    injecteds.push(path.join(__dirname, '../injected/global_module.js'));
    switch (datavalue.id) {
        case 0:
            injecteds.push(path.join(__dirname, '../injected/injectedScript.js'));
            break;
        case 1:
            injecteds.push(path.join(__dirname, '../injected/injectedScriptForWhatsAppOne.js'));
            break;
    }
    function injection(Path, Text) {
        let ext = Path.split('.').pop();
        let Script = document.createElement(ext === 'css' ? 'style' : 'script');
        if (ext == 'js') {
            ext = 'javascript';
        }
        Script.setAttribute('type', 'text/' + ext);
        Script.setAttribute('_src', Path);
        if (ext === 'css') {
            Script.setAttribute('rel', 'stylesheet');
        }
        Script.innerHTML = Text;
        document.head.appendChild(Script);
    }
    for (let i = 0; i < injecteds.length; i++) {
        let file = injecteds[i];
        let content = fs.readFileSync(file, 'utf8');
        injection(file, content);
    }
}

function Main() {
    return new Promise(async (resolve) => {
        console.log('preload.js_Main');
        await new Promise(async (resolve) => {
            let timer = setInterval(() => {
                let div = document.getElementById('winid');
                if (div) {
                    __winId__ = div.innerHTML;
                    datavalue = JSON.parse(div.getAttribute('datavalue'));
                    clearInterval(timer);
                    resolve();
                }
            }, 10);
        });
        ipcRenderer.on(__winId__ + '_message', (event, message) => {
            messageId[message.messageId] = message.data;
        });
        injectedScript();
        resolve();
    });
}

contextBridge.exposeInMainWorld('Main', function (action, param) {
    if (param == null) {
        param = {};
    }
    return new Promise(async (resolve) => {
        let r = await sendMain(action, param);
        resolve(r);
    });
});

contextBridge.exposeInMainWorld('_getWinId_', function () {
    return __winId__;
});

contextBridge.exposeInMainWorld('electronAction', function (action, param) {
    return globalVariables.get('Init').action;
});

contextBridge.exposeInMainWorld('getAdminHost', function (action, param) {
    return fs.readFileSync(path.join(__dirname, '../config/adminhost.txt'), 'utf8');
});

contextBridge.exposeInMainWorld('axios', function (param) {
    return new Promise(async (resolve) => {
        let { url, method, data, headers } = param;
        let r = null;
        try {
            r = await axios({
                url,
                method,
                data,
                headers
            });
        } catch (e) {
            resolve(r);
            return;
        }
        resolve(r);
    });
});

Main();
