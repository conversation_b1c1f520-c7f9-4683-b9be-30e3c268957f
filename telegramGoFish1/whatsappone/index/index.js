const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const iconv = require('iconv-lite');
const { spawn, exec, fork } = require('child_process');
const global_module = require('../../module/global_module');

var globalVariables = new Map();
globalVariables.set('electronData', path.join(__dirname, '../../', 'temp/electronDataForWhatsAppOne'));

let numberThreads = 3;
var winObj = {};
var readyOkWin = {};

function init() {
    let dir = globalVariables.get('electronData');
    try {
        if (fs.existsSync(dir)) {
            fs.rmdirSync(dir, { recursive: true });
        }
        fs.mkdirSync(dir);
    } catch (e) {}
    initConfig();
    initWin();
    setInterval(() => {
        let { ready, busy } = getWinstatusInfo();
        if (ready !== globalVariables.get('temp_WinReady') || busy !== globalVariables.get('temp_WinBusy')) {
            console.info(getNowFormatDate(), '就绪窗口数量:', ready, '繁忙窗口数量:', busy);
            globalVariables.set('temp_WinReady', ready);
            globalVariables.set('temp_WinBusy', busy);
        }
    }, 5000);
}

function getWinstatusInfo() {
    let winObjLength = Object.keys(winObj).length;
    let readyOkWinLength = Object.keys(readyOkWin).length;
    return { ready: readyOkWinLength, busy: winObjLength - readyOkWinLength };
}

function initConfig() {
    let dir = path.join(__dirname, '../../config');
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
    }
    if (!fs.existsSync(path.join(__dirname, '../../config/WhatsAppOneNumberThreads.txt'))) {
        fs.writeFileSync(path.join(__dirname, '../../config/WhatsAppOneNumberThreads.txt'), '10');
    }
    numberThreads = parseInt(fs.readFileSync(path.join(__dirname, '../../config/WhatsAppOneNumberThreads.txt'), 'utf8'));
    if (numberThreads > 20) {
        numberThreads = 20;
    }
}

function initWin() {
    return new Promise(async (resolve) => {
        for (let i = 0; i < numberThreads; i++) {
            createWin();
            await new Promise((resolve) => {
                let timer = setInterval(() => {
                    let { busy } = getWinstatusInfo();
                    if (busy >= 2) {
                        return;
                    }
                    clearInterval(timer);
                    resolve();
                }, 500);
            });
        }
        resolve();
    });
}

function createWin() {
    let winID = getWinId();
    winObj[winID] = { winID };
    let cwd = path.join(__dirname, '../../child/');
    let args = ['--id=' + winID];
    let serverPort = 0;
    let electronData = null;
    let ready = false;
    let childFun = {
        init: (param) => {
            return new Promise(async (resolve) => {
                serverPort = param.serverPort;
                electronData = param.electronData;
                resolve({ status: 0, action: 'init' });
            });
        },
        log: (param) => {
            return new Promise(async (resolve) => {
                console.info(param);
                resolve({ status: 0, action: 'log' });
            });
        },
        ready: (param) => {
            return new Promise(async (resolve) => {
                ready = true;
                winObj[winID] = { winID, childProcess, childFun };
                readyOkWin[winID] = { winID, childProcess, childFun };
                resolve({ status: 0, action: 'ready' });
            });
        },
        close: () => {
            return new Promise(async (resolve) => {
                if (winObj[winID]) {
                    delete winObj[winID];
                }
                if (readyOkWin[winID]) {
                    delete readyOkWin[winID];
                }
                try {
                    if (childProcess) {
                        childProcess.kill();
                    }
                } catch (e) {}
                if (electronData) {
                    setTimeout(() => {
                        delDir(electronData);
                    }, 1000 * 30);
                }
                resolve({ status: 0, action: 'close' });
            });
        },
        packDataDone: (param) => {
            return new Promise(async (resolve) => {
                try {
                    console.info('登录成功,打包完毕!', param.winID, param.uuid, winObj[param.winID].phone);
                } catch (e) {}
                resolve({ status: 0, action: 'packDataDone' });
            });
        },
        _send_: (jsonObj) => {
            return new Promise(async (resolve) => {
                try {
                    let httpResponse = await fetch('http://127.0.0.1:' + serverPort + '/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(jsonObj)
                    });
                    resolve({ status: 0, data: httpResponse.data, serverPort });
                    return;
                } catch (e) {
                    // console.error(e);
                }
                resolve({ status: -1, data: '', serverPort });
            });
        }
    };
    let WhatsApponeFilePath = path.join(__dirname, '../../child/', 'WhatsAppone.js');
    let childProcess = spawn(app.getPath('exe'), [WhatsApponeFilePath, ...args], { cwd });
    childProcess.stdout.on('data', (data) => {
        return new Promise(async (resolve) => {
            let text = data.toString();
            try {
                let obj = JSON.parse(text);
                let funName = obj.funName;
                let param = obj.param;
                let fun = childFun[funName];
                let r = '';
                if (fun) {
                    r = await fun(param);
                }
                r.messageId = obj.messageId;
                childFun._send_(r);
            } catch (e) {}
            resolve();
        });
    });
    childProcess.on('close', (code) => {
        childFun.close();
    });
    childProcess.on('exit', (code) => {
        childFun.close();
    });
    setTimeout(() => {
        if (!ready) {
            console.info('就绪超时,开始重建!', winID);
            childFun.close();
            createWin();
        }
    }, 1000 * 60);
}

function getWinId() {
    let id = global_module.randomString(32);
    while (winObj[id]) {
        id = global_module.randomString(32);
    }
    return id;
}

function execCommand(command) {
    return new Promise((resolve, reject) => {
        exec(command, (err, stdout, stderr) => {
            if (err) {
                resolve(err);
            } else {
                resolve(stdout);
            }
        });
    });
}

function delDir(path) {
    return new Promise(async (resolve) => {
        let exePath = 'C:\\Users\\<USER>\\Desktop\\文件复制增强工具\\FastCopy.exe';
        let _args = '/cmd=Delete /auto_close /force_close /no_confirm_del /no_confirm_stop';
        let command = `${exePath} ${_args} "${path}"`;
        await execCommand(command);
        resolve();
    });
}

function getRandomNum(minNum, maxNum) {
    return Math.floor(Math.random() * (maxNum - minNum + 1) + minNum);
}

function getNowFormatDate() {
    let date = new Date();
    let seperator1 = '-';
    let seperator2 = ':';
    let month = date.getMonth() + 1;
    let strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = '0' + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate;
    }
    let Hour = date.getHours();
    if (Hour >= 0 && Hour <= 9) {
        Hour = '0' + Hour;
    }
    let Minute = date.getMinutes();
    if (Minute >= 0 && Minute <= 9) {
        Minute = '0' + Minute;
    }
    let Second = date.getSeconds();
    if (Second >= 0 && Second <= 9) {
        Second = '0' + Second;
    }
    let currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate + ' ' + Hour + seperator2 + Minute + seperator2 + Second;
    return currentdate;
}

function getVerificationCode(op) {
    return new Promise(async (resolve) => {
        let { req, res, urlObj, affiliated, fileData, Sql: SqlObj } = op;
        if (req.method !== 'POST') {
            resolve({ status: -1, msg: '请求类型不正确!' });
            return;
        }
        let data = req?.body;
        try {
            if (typeof data === 'string') {
                data = JSON.parse(body);
            }
        } catch (e) {}
        if (!data || typeof data === 'string') {
            resolve({ status: -2, msg: '参数提交不正确!' });
            return;
        }
        let { uuid, phone, host } = data;
        if (!uuid || !phone || !host) {
            resolve({ status: -3, msg: '参数提交不正确!' });
            return;
        }
        let obj = readyOkWin[Object.keys(readyOkWin)[0]];
        if (!obj) {
            let random = getRandomNum(100, 500);
            await new Promise((resolve) => {
                let timer = setInterval(() => {
                    obj = readyOkWin[Object.keys(readyOkWin)[0]];
                    if (!obj || obj.winID == null) {
                        return;
                    }
                    clearTimeout(timer);
                    resolve();
                }, random);
            });
        }
        let winID = obj.winID;
        delete readyOkWin[winID];
        (async () => {
            createWin();
        })();
        let { childFun } = winObj[winID];
        winObj[winID].startTime = new Date().getTime();
        console.info('收到任务:', '来自[' + host + '](' + phone + ')', '使用->', winID);
        setTimeout(() => {
            childFun.close();
        }, 3 * 60 * 1000);
        let MainResolve = resolve;
        childFun.getVerificationCodeComplete = function (param) {
            return new Promise((resolve) => {
                let costTime = new Date().getTime() - winObj[winID].startTime;
                console.info('任务完成:', '来自[' + host + '](' + phone + ')', '使用->', winID, '耗时->', costTime, 'ms');
                resolve({ status: 0, action: 'getVerificationCodeComplete' });
                MainResolve(param.code);
            });
        };
        winObj[winID].phone = phone;
        let {
            status,
            data: retData,
            serverPort
        } = await childFun._send_({
            funName: 'executeJavaScript',
            param: {
                code: `(()=>{window.getVerificationCode("${uuid}", "${phone}","${host}");})();`
            }
        });
        if (status != 0) {
            console.error('传递执行代码失败!', '端口号:', serverPort);
        }
    });
}

module.exports = { init, getVerificationCode };
